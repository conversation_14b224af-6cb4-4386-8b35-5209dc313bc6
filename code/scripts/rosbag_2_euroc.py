#!/usr/bin/env python3

import argparse
import errno
import os

import cv2
import numpy as np
import yaml
from cv_bridge import CvBridge
from rosbags.rosbag2 import Reader
from rosbags.typesys import Stores, get_typestore

CAM_FOLDER_NAME = "cam0"
IMU_FOLDER_NAME = "imu0"
DATA_CSV = "data.csv"
SENSOR_YAML = "sensor.yaml"
BODY_YAML = "body.yaml"

typestore = get_typestore(Stores.LATEST)

# NOTE: These are placeholder sensor parameters. -1 is used as the default value.
# For rd-vio, these are typically loaded from configuration files, not modified here.
CAM_SENSOR_YAML = dict(
    sensor_type="camera",
    comment="VI-Sensor cam0 (MT9M034)",  # Default comment, updated later
    T_BS=dict(
        cols=4,
        rows=4,
        data=[-1.0] * 16,
    ),
    rate_hz=-1,
    resolution=[-1, -1],
    camera_model="pinhole",
    intrinsics=[-1.0, -1.0, -1.0, -1.0],
    distortion_model="radial-tangential",
    distortion_coefficients=[-1.0, -1.0, -1.0, -1.0],
)

# NOTE: These are placeholder sensor parameters. -1 is used as the default value.
# For rd-vio, these are typically loaded from configuration files, not modified here.
IMU_SENSOR_YAML = dict(
    sensor_type="imu",
    comment="IMU sensor parameters (placeholder)",  # Updated comment
    T_BS=dict(cols=4, rows=4, data=[-1.0] * 16),
    rate_hz=-1,
    gyroscope_noise_density=-1.0,
    gyroscope_random_walk=-1.0,
    accelerometer_noise_density=-1.0,
    accelerometer_random_walk=-1.0,
)


def get_topic_connection_from_rosbag(rosbag_path, image_topic_arg, imu_topic_arg):
    """
    Extracts the specified single camera and single IMU topic connections from a ROS 2 bag.

    Args:
        rosbag_path (str): Path to the ROS 2 bag file.
        image_topic_arg (str): The single camera topic name to extract.
        imu_topic_arg (str): The single IMU topic name to extract.

    Returns:
        tuple[Connection | None, Connection | None]: A tuple containing:
            - camera_connection: The connection object for the camera topic, or None if not found.
            - imu_connection: The connection object for the IMU topic, or None if not found.
    """
    camera_connection = None
    imu_connection = None
    # Define acceptable message types
    image_msg_types = {"sensor_msgs/msg/Image", "sensor_msgs/msg/CompressedImage"}
    imu_msg_type = "sensor_msgs/msg/Imu"

    with Reader(rosbag_path) as reader:
        for connection in reader.connections:
            # Check for the specified image topic with valid message types
            if (
                not camera_connection
                and connection.topic == image_topic_arg
                and connection.msgtype in image_msg_types
            ):
                camera_connection = connection
            # Check for the specified IMU topic with the valid message type
            elif (
                not imu_connection
                and connection.topic == imu_topic_arg
                and connection.msgtype == imu_msg_type
            ):
                imu_connection = connection
            # Optimization: stop searching if both topics have been found
            if camera_connection and imu_connection:
                break

    return camera_connection, imu_connection


def mkdirs_without_exception(path):
    """
    Creates a directory path including intermediate directories if it doesn't exist.
    Ignores the error if the directory already exists.

    Args:
        path (str): The directory path to create.

    Raises:
        OSError: Raises any OSError encountered during directory creation,
                 except for errno.EEXIST (path already exists).
    """
    try:
        os.makedirs(path)
    except OSError as e:
        # Ignore error if the directory already exists
        if e.errno != errno.EEXIST:
            print(f"Error creating directory {path}: {e}")
            raise


def setup_dataset_dirs(rosbag_path, output_path, camera_connection, imu_connection):
    """
    Sets up the EuRoC dataset directory structure ('mav0/cam0/', 'mav0/imu0/')
    and metadata files (sensor.yaml, data.csv, body.yaml) for the single
    camera and IMU topics.

    Args:
        rosbag_path (str): Path to the input ROS 2 bag file (used for body.yaml comment).
        output_path (str): Path to the root directory for the output dataset.
        camera_connection (Connection | None): The connection object for the camera topic.
                                               If None, the camera directory is not created.
        imu_connection (Connection | None): The connection object for the IMU topic.
                                            If None, the IMU directory is not created.

    Returns:
        tuple[str | None, str | None]: A tuple containing:
            - cam_folder_path (str | None): Path to the created camera data folder ('.../mav0/cam0'), or None.
            - imu_folder_path (str | None): Path to the created IMU data folder ('.../mav0/imu0'), or None.
    """
    # Create the base 'mav0' directory structure
    dirname = os.path.splitext(os.path.basename(rosbag_path))[0]
    base_path = os.path.join(output_path, dirname, "mav0")
    mkdirs_without_exception(base_path)

    cam_folder_path = None
    # Setup camera directory and files if a camera connection was found
    if camera_connection:
        cam_folder_path = os.path.join(base_path, CAM_FOLDER_NAME)
        cam_data_path = os.path.join(cam_folder_path, "data")
        mkdirs_without_exception(cam_folder_path)
        mkdirs_without_exception(cam_data_path)

        # Create camera data.csv with header
        with open(os.path.join(cam_folder_path, DATA_CSV), "w+") as outfile:
            outfile.write("#timestamp [ns],filename\n")  # Add newline

        # Create camera sensor.yaml
        with open(os.path.join(cam_folder_path, SENSOR_YAML), "w+") as outfile:
            outfile.write("%YAML:1.0\n")
            cam_yaml = CAM_SENSOR_YAML.copy()
            topic_base_name = camera_connection.topic.strip("/").replace("/", "_")
            cam_yaml["comment"] = (
                f"camera {CAM_FOLDER_NAME}: {topic_base_name}"  # Use fixed folder name
            )
            yaml.dump(cam_yaml, outfile, default_flow_style=None)

    imu_folder_path = None
    # Setup IMU directory and files if an IMU connection was found
    if imu_connection:
        imu_folder_path = os.path.join(base_path, IMU_FOLDER_NAME)
        mkdirs_without_exception(imu_folder_path)

        # Create IMU data.csv with header
        with open(os.path.join(imu_folder_path, DATA_CSV), "w+") as outfile:
            outfile.write(
                "#timestamp [ns],w_RS_S_x [rad s^-1],w_RS_S_y [rad s^-1],w_RS_S_z [rad s^-1],"
                "a_RS_S_x [m s^-2],a_RS_S_y [m s^-2],a_RS_S_z [m s^-2]\n"  # Add newline
            )

        # Create IMU sensor.yaml
        with open(os.path.join(imu_folder_path, SENSOR_YAML), "w+") as outfile:
            outfile.write("%YAML:1.0\n")
            imu_yaml = IMU_SENSOR_YAML.copy()
            topic_base_name = imu_connection.topic.strip("/").replace("/", "_")
            imu_yaml["comment"] = (
                f"imu {IMU_FOLDER_NAME}: {topic_base_name}"  # Use fixed folder name
            )
            yaml.dump(imu_yaml, outfile, default_flow_style=None)

    # Create body.yaml in the base 'mav0' directory
    with open(os.path.join(base_path, BODY_YAML), "w+") as outfile:
        outfile.write("%YAML:1.0\n")
        body_yaml = dict(
            comment=f"Automatically generated dataset using Rosbag2Euroc, source rosbag: "
            f"{os.path.basename(rosbag_path)}"
        )
        yaml.dump(body_yaml, outfile, default_flow_style=None)

    return cam_folder_path, imu_folder_path


def rosbag_2_euroc(
    rosbag_path, output_path, image_topic_arg, imu_topic_arg, time_offset_seconds=0.0
):
    """
    Converts the specified single image topic and single IMU topic from a
    ROS 2 bag file into the EuRoC MAV dataset format.

    It extracts messages, saves images as PNG files, and writes timestamps
    and sensor data to CSV files ('data.csv') within the appropriate
    'cam0' and 'imu0' directories. It also generates the required
    'sensor.yaml' and 'body.yaml' metadata files.

    Args:
        rosbag_path (str): Path to the input ROS 2 bag file.
        output_path (str): Path to the root directory where the output dataset
                           (e.g., 'bag_name/mav0/') will be created.
        image_topic_arg (str): The specific camera topic name to convert
                               (e.g., '/cam0/image_raw'). Supports both
                               sensor_msgs/Image and sensor_msgs/CompressedImage.
        imu_topic_arg (str): The specific IMU topic name to convert
                             (e.g., '/imu0/data'). Supports sensor_msgs/Imu.
        time_offset_seconds (float): Number of seconds to skip from the beginning
                                   of the bag file. Default is 0.0 (no offset).
    """
    if not os.path.exists(rosbag_path):
        raise FileNotFoundError(f"Input rosbag not found at: {rosbag_path}")

    camera_connection, imu_connection = get_topic_connection_from_rosbag(
        rosbag_path, image_topic_arg, imu_topic_arg
    )

    if camera_connection:
        print(
            f"Found Camera Topic: {camera_connection.topic} (Type: {camera_connection.msgtype})"
        )
    else:
        print(
            f"WARNING: Specified camera topic '{image_topic_arg}' not found or has unsupported message type."
        )

    if imu_connection:
        print(
            f"Found IMU Topic: {imu_connection.topic} (Type: {imu_connection.msgtype})"
        )
    else:
        print(
            f"WARNING: Specified IMU topic '{imu_topic_arg}' not found or has unsupported message type."
        )

    if not camera_connection and not imu_connection:
        print(
            "ERROR: Neither the specified camera nor IMU topic was found. Cannot proceed. Exiting."
        )
        return

    cam_folder_path, imu_folder_path = setup_dataset_dirs(
        rosbag_path, output_path, camera_connection, imu_connection
    )

    if cam_folder_path:
        print(f"Output Camera Folder: {cam_folder_path}")
    if imu_folder_path:
        print(f"Output IMU Folder: {imu_folder_path}")

    bridge = CvBridge()

    with Reader(rosbag_path) as reader:
        start_time = (
            reader.start_time + int(time_offset_seconds * 1e9)
            if time_offset_seconds > 0.0
            else None
        )
        # Process camera messages if the topic connection was found
        if camera_connection:
            print(f"Converting camera messages from topic: {camera_connection.topic}")
            print(f"Storing image files in: {os.path.join(cam_folder_path, 'data')}")
            print(f"Appending records to: {os.path.join(cam_folder_path, DATA_CSV)}")

            processed_count = 0

            with open(os.path.join(cam_folder_path, DATA_CSV), "a") as outfile:
                for connection, _, data in reader.messages(
                    connections=[camera_connection], start=start_time
                ):
                    try:
                        msg = typestore.deserialize_cdr(data, connection.msgtype)
                        msg_timestamp = int(
                            msg.header.stamp.sec * 1e9 + msg.header.stamp.nanosec
                        )

                        # Convert ROS Image/CompressedImage to OpenCV image
                        if connection.msgtype == "sensor_msgs/msg/CompressedImage":
                            cv_image = bridge.compressed_imgmsg_to_cv2(msg)
                        elif connection.msgtype == "sensor_msgs/msg/Image":
                            # Keep original encoding if uncompressed (e.g., mono8, bgr8)
                            cv_image = bridge.imgmsg_to_cv2(
                                msg, desired_encoding="passthrough"
                            )
                        else:
                            print(
                                f"Warning: Unexpected message type {connection.msgtype} encountered for camera "
                                f"topic {connection.topic}. Skipping."
                            )
                            continue

                        # Ensure image is grayscale uint8 for EuRoC consistency
                        if len(cv_image.shape) == 3 and cv_image.shape[2] == 3:
                            cv_image = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
                        elif cv_image.dtype != np.uint8:
                            cv_image = cv2.normalize(
                                cv_image, None, 0, 255, cv2.NORM_MINMAX
                            ).astype(np.uint8)

                        image_filename = f"{msg_timestamp}.png"
                        image_save_path = os.path.join(
                            cam_folder_path, "data", image_filename
                        )
                        cv2.imwrite(image_save_path, cv_image)

                        outfile.write(f"{msg_timestamp},{image_filename}\n")
                        processed_count += 1

                    except Exception as e:
                        print(
                            f"Error processing image message at timestamp {msg_timestamp} from topic "
                            f"{connection.topic}: {e}"
                        )

            print(f"Camera messages: {processed_count} processed")

        # Process IMU messages if the topic connection was found
        if imu_connection:
            print(f"Converting IMU messages from topic: {imu_connection.topic}")
            print(f"Appending records to: {os.path.join(imu_folder_path, DATA_CSV)}")

            processed_count = 0

            with open(os.path.join(imu_folder_path, DATA_CSV), "a") as outfile:
                # Filter messages to only read from the target IMU connection
                for connection, _, data in reader.messages(
                    connections=[imu_connection], start=start_time
                ):
                    try:
                        msg = typestore.deserialize_cdr(data, connection.msgtype)
                        msg_timestamp = int(
                            msg.header.stamp.sec * 1e9 + msg.header.stamp.nanosec
                        )

                        outfile.write(
                            f"{msg_timestamp},"  # Timestamp [ns]
                            f"{msg.angular_velocity.x},"  # Gyro X [rad/s]
                            f"{msg.angular_velocity.y},"  # Gyro Y [rad/s]
                            f"{msg.angular_velocity.z},"  # Gyro Z [rad/s]
                            f"{msg.linear_acceleration.x},"  # Accel X [m/s^2]
                            f"{msg.linear_acceleration.y},"  # Accel Y [m/s^2]
                            f"{msg.linear_acceleration.z}\n"  # Accel Z [m/s^2] and newline
                        )
                        processed_count += 1

                    except Exception as e:
                        print(
                            f"Error processing IMU message at timestamp {msg_timestamp} from topic "
                            f"{connection.topic}: {e}"
                        )

            print(f"IMU messages: {processed_count} processed")

    print("\nConversion completed successfully.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Convert a single ROS 2 image topic and a single IMU topic "
        "from a rosbag into the EuRoC MAV dataset format.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument(
        "rosbag_path", help="Path to the input ROS 2 bag directory or sqlite3 file."
    )
    parser.add_argument(
        "--image_topic",
        required=True,
        help="The specific image topic to process (e.g., '/camera/image_raw'). "
        "Supports 'sensor_msgs/msg/Image' and 'sensor_msgs/msg/CompressedImage'.",
    )
    parser.add_argument(
        "--imu_topic",
        required=True,
        help="The specific IMU topic to process (e.g., '/imu/data'). Supports 'sensor_msgs/msg/Imu'.",
    )
    parser.add_argument(
        "-o",
        "--output-path",
        default="./",
        help="Path to the output directory where the EuRoC dataset structure "
        "(e.g., 'rosbag_name/mav0/') will be created.",
    )
    parser.add_argument(
        "--time-offset",
        type=float,
        default=0.0,
        help="Number of seconds to skip from the beginning of the bag file. "
        "Only messages after this time offset will be processed. (default: 0.0)",
    )
    args = parser.parse_args()

    print("Starting Rosbag to EuRoC conversion...")
    print(f"Input Rosbag: '{args.rosbag_path}'")
    print(f"Target Image Topic: '{args.image_topic}'")
    print(f"Target IMU Topic: '{args.imu_topic}'")
    print(f"Output Directory: '{os.path.abspath(args.output_path)}'")
    if args.time_offset > 0.0:
        print(f"Time Offset: {args.time_offset} seconds")

    try:
        rosbag_2_euroc(
            args.rosbag_path,
            args.output_path,
            args.image_topic,
            args.imu_topic,
            args.time_offset,
        )
        print("Done.")
    except FileNotFoundError as e:
        print(f"\nError: {e}")
        print("Please ensure the rosbag path is correct.")
    except Exception as e:
        print(f"\nAn unexpected error occurred during conversion: {e}")
