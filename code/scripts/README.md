# Rosbag to EuRoC Format Converter
This script converts data from a ROS 2 bag file containing specific image and IMU topics into the EuRoC MAV dataset format.

## Requirements

*   Python 3
*   `rosbags` library (`pip install rosbags`)
*   `opencv-python` (`pip install opencv-python`)
*   `numpy` (`pip install numpy`)
*   `pyyaml` (`pip install pyyaml`)
*   `cv_bridge` (Typically installed with ROS 2, or potentially via `pip install cv-bridge`)

## Usage

The script takes the path to the rosbag, the specific image topic, the specific IMU topic, the time offset in seconds, and an optional output directory as arguments.

```bash
python rosbag2euroc.py <path_to_rosbag> --image-topic <your_image_topic> --imu-topic <your_imu_topic> [--time-offset <offset_in_seconds> -o <output_directory>]
```

**Example:**

```bash
# Convert data from my_robot.bag, using /camera/image_raw and /imu/data, saving to ./euroc_data
python rosbag2euroc.py /path/to/my_robot.bag --image-topic /camera/image_raw --imu-topic /imu/data -o ./euroc_data
```

## Arguments

*   `rosbag_path`: (Required) Path to the input ROS 2 bag directory or sqlite3 file.
*   `--image-topic`: (Required) The specific image topic to process (e.g., '/camera/image_raw'). Supports `sensor_msgs/msg/Image` and `sensor_msgs/msg/CompressedImage`.
*   `--imu-topic`: (Required) The specific IMU topic to process (e.g., '/imu/data'). Supports `sensor_msgs/msg/Imu`.
*   `--time-offset`: (Optional) Number of seconds to skip from the beginning of the bag file
*   `-o, --output-path`: (Optional) Path to the output directory where the EuRoC dataset structure (e.g., 'rosbag_name/mav0/') will be created. Defaults to the current directory (`./`).
*   `-h, --help`: Show the help message and exit.

## Output Structure

The script will create a directory structure like this:

## Using the Output with rd-vio

To use the generated EuRoC dataset with the rd-vio project (presumably running inside Docker via `building/local/run_image.sh`), follow these steps:

1.  **Mount the Generated Data into Docker:**
    Modify the `docker run` command within your `building/local/run_image.sh` script. You need to mount the **parent directory** of the generated `mav0` folder (e.g., `<output_directory>/<rosbag_name>/` from the example above) to the `/dataset` path inside the container. Replace the existing `/dataset` volume mount line:

    ```bash
    docker run \
        --entrypoint /bin/bash \
        --interactive \
        --name "${LIB_NAME}" \
        --network host \
        --rm \
        --tty \
        --volume "OUTPUT_FOLDER/:/dataset" \
        "${TARGET_IMAGE}"
    ```
    *Replace `$OUTPUT_FOLDER/` with the actual absolute path to the directory containing your generated `mav0` folder.* Make sure your calibration file (e.g., `astro_1_1_0_0.yaml`) and configuration file (e.g., `setting.yaml`) are also present in this host directory (`$OUTPUT_FOLDER/` in the example) so they are accessible under `/dataset` in the container.

2.  **Build and Run the Docker Image:**
    ```bash
    ./building/local/build_image.sh build
    ./building/local/run_image.sh build
    ```
    This will drop you into a bash shell inside the container.

3.  **Navigate to Build Directory:**
    ```bash
    cd build-Release
    ```

4.  **Run rd-vio:**
    Execute the `euroc_main` program, pointing it to the dataset mounted at `/dataset` and specifying your calibration and configuration files (which should also be under `/dataset` due to the volume mount).
    ```bash
    ./euroc_main \
        --calib_file /dataset/CALIB_FILE.yaml \
        --config_file /dataset/setting.yaml \
        --data_directory /dataset/mav0/ \
        --log_file /dataset/log.txt \
        --odometry_log_file /dataset/odometry.csv
    ```
    *   `--calib_file`: Path inside the container to your sensor calibration YAML file.
    *   `--config_file`: Path inside the container to your rd-vio settings YAML file.
    *   `--data_directory`: Path inside the container to the `mav0` directory containing the `cam0`, `imu0`, and `body.yaml` generated by the conversion script.
    *   `--log_file`: Path inside the container where the output log will be saved.
    *   `--odometry_log_file`: Path inside the container where the odometry in csv format will be saved.