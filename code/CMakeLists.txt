cmake_minimum_required(VERSION VERSION 3.8)

if(NOT DEFINED LIB_VERSION)
    set(LIB_VERSION "0.0.0")
endif()

project(spl-rd-vio VERSION ${LIB_VERSION} LANGUAGES CXX)

option(BUILD_APPLICATIONS "Build application executables" ON)
option(BUILD_TESTING "Build test executables" ON)
option(THREADING "Enable multi-threading" ON)
option(LOG_STATISTICS "Enable statistics logging" OFF)

cmake_policy(SET CMP0072 NEW)
cmake_policy(SET CMP0079 NEW)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wno-unused-result -Wno-ignored-attributes")
set(CMAKE_CXX_FLAGS "${CMAKE_C_FLAGS} -Wnarrowing -Werror=narrowing -Wno-unused-result -Wno-ignored-attributes")

set(CMAKE_THREAD_LIBS_INIT "-lpthread")

set(CMAKE_SKIP_BUILD_RPATH FALSE)
set(CMAKE_BUILD_WITH_INSTALL_RPATH FALSE)
set(CMAKE_INSTALL_RPATH "/opt/spl-packages/")
set(CMAKE_INSTALL_RPATH_USE_LINK_PATH TRUE)

find_package(Threads REQUIRED)
find_package(OpenCV 4 REQUIRED)
find_package(Eigen3 REQUIRED)
find_package(Ceres REQUIRED)
find_package(yaml-cpp REQUIRED)
find_package(spl-camera-models REQUIRED)
find_package(spl-logging REQUIRED)

if(THREADING)
    add_definitions(-DUSE_MULTI_THREADING)
endif()
option(TIMING "Enable timing" OFF)
if(TIMING)
    add_definitions(-DENABLE_TIMING)
endif()
if(LOG_STATISTICS)
    add_definitions(-DLOG_STATISTICS)
endif()

include(CTest) # NOTE(Jack): needs to come before adding any subdirectory otherwise not all tests will be discovered

add_subdirectory(src)


if (BUILD_APPLICATIONS)
    set(APPLICATION_LIST
           euroc_main
    )

    foreach (APPLICATION_NAME ${APPLICATION_LIST})
        set(EXEC_NAME ${APPLICATION_NAME})

        add_executable(${EXEC_NAME})
        target_sources(${EXEC_NAME} PRIVATE
                "apps/${EXEC_NAME}.cpp"
        )
        target_link_libraries(${EXEC_NAME} PRIVATE
                ${PROJECT_NAME}
        )
    endforeach (APPLICATION_NAME)
endif (BUILD_APPLICATIONS)


if (BUILD_TESTING)
    include(FetchContent)
    FetchContent_Declare(
            googletest
            GIT_REPOSITORY https://github.com/google/googletest.git
            GIT_TAG release-1.12.1
    )
    # For Windows: Prevent overriding the parent project's compiler/linker settings
    set(gtest_force_shared_crt ON CACHE BOOL "" FORCE)
    FetchContent_MakeAvailable(googletest)

    if(THREADING)
        set(TEST_LIST
            test_types
        )
    else()
        set(TEST_LIST
            test_euroc
            test_types
        )
    endif()

    foreach (TEST_NAME ${TEST_LIST})
        set(EXEC_NAME ${TEST_NAME})

        add_executable(${EXEC_NAME})
        target_sources(${EXEC_NAME} PRIVATE "test/${EXEC_NAME}.cpp")
        target_link_libraries(${EXEC_NAME} PRIVATE
                ${PROJECT_NAME}
                GTest::gtest_main
        )
        add_test(
                NAME ${EXEC_NAME}
                COMMAND $<TARGET_FILE:${EXEC_NAME}>
                WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        )
        # Disable timeout for all tests
        set_tests_properties(${EXEC_NAME} PROPERTIES TIMEOUT 0) 
    endforeach (TEST_NAME)
endif (BUILD_TESTING)