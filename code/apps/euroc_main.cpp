#include <cstring>
#include <optional>
#include <string>

#include <rdvio/testing_util/integration/euroc_runner.hpp>

struct EurocMainParameters {
  std::optional<std::string> calib_file;
  std::optional<std::string> config_file;
  std::optional<std::string> data_directory;
  std::optional<std::string> log_file;
  std::optional<std::string> odometry_log_file;
  double start_offset_sec{0.0};
  double duration_sec{600.0};
};

EurocMainParameters LoadCli(const int argc, const char* const* const argv) {
  EurocMainParameters parameters;
  for (int i = 0; i < argc; ++i) {
    if (strcmp(argv[i], "--calib_file") == 0 and i + 1 < argc) {
      parameters.calib_file = argv[i + 1];
    }
    if (strcmp(argv[i], "--config_file") == 0 and i + 1 < argc) {
      parameters.config_file = argv[i + 1];
    }
    if (strcmp(argv[i], "--data_directory") == 0 and i + 1 < argc) {
      parameters.data_directory = argv[i + 1];
    }
    if (strcmp(argv[i], "--log_file") == 0 and i + 1 < argc) {
      parameters.log_file = argv[i + 1];
    }
    if (strcmp(argv[i], "--odometry_log_file") == 0 and i + 1 < argc) {
      parameters.odometry_log_file = argv[i + 1];
    }
    if (strcmp(argv[i], "--start_offset_sec") == 0 and i + 1 < argc) {
      parameters.start_offset_sec = std::stod(argv[i + 1]);
    }
    if (strcmp(argv[i], "--duration_sec") == 0 and i + 1 < argc) {
      parameters.duration_sec = std::stod(argv[i + 1]);
    }
  }

  if (not(parameters.calib_file.has_value() and parameters.config_file.has_value() and
          parameters.data_directory.has_value() and parameters.log_file.has_value() and
          parameters.odometry_log_file.has_value())) {
    throw std::runtime_error("Not all required arguments provided to LoadCli()!");
  }

  return parameters;
}

int main(const int argc, const char* const* const argv) {
  const EurocMainParameters parameters{LoadCli(argc, argv)};

  rdvio::EurocRunner(parameters.calib_file.value(), parameters.config_file.value(), parameters.data_directory.value(),
                     parameters.log_file.value(), parameters.odometry_log_file.value(), parameters.start_offset_sec,
                     parameters.duration_sec);

  return 0;
}