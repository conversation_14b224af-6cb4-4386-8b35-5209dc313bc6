sliding_window:
  sliding_window_size: 15
  subframe_size: 5
  force_keyframe_landmarks: 200
  max_feature_height: 50
  parsac_flag: true
  parsac_keyframe_check_size: 1

feature_tracker:
  min_keypoint_distance: 10.0
  max_keypoint_detection: 400
  max_init_frames: 60
  max_frames: 100
  predict_keypoints: true
  rotation_misalignment_threshold: 0.02
  rotation_ransac_threshold: 10
  sliding_window_tracker_frequent: 1
  clahe:
    clip_limit: 6.0
    width: 8
    height: 8
  optical_flow:
    level_num: 3
    max_iterations: 30
    epsilon: 0.01
    window_size: 21
    max_movement_factor: 0.25
    cross_check_error_th: 0.5
    reuse_input_image: true
    draw_optical_flow: false
    optical_flow_output_path: ""

initializer:
  keyframe_num: 8
  keyframe_gap: 5
  min_landmarks: 30
  refine_imu: true
  sfm:
    min_matches: 50
    min_parallax: 10.0
    min_triangulation: 20
    random: 648

solver:
  max_num_iterations: 30
  max_solver_time_in_seconds: 0.2
