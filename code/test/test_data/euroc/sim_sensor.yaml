imu:
  # Values from https://github.com/microsoft/AirSim/discussions/3523#discussioncomment-525240
  # inertial sensor noise model parameters (static)
  gyroscope_noise_density: 8.7266462e-5      # [rad / s / sqrt(Hz)]
  gyroscope_random_walk:   9.9735023e-7        # [rad / s^2 / sqrt(Hz)]
  accelerometer_noise_density: 0.002353596    # [m / s^2 / sqrt(Hz)]
  accelerometer_random_walk:   1.2481827e-5  # [m / s^3 / sqrt(Hz)]

cam0:
  do_image_rectification: false
  resolution: [1280,720]        # resolution of camera 
  camera_model: pinhole         # camera model
  distortion_model: radtan      # distortion model
  intrinsics: [640, 640, 640, 360] # fu, fv, cu, cv
  distortion_coeffs: [0.0, 0.0, 0.0, 0.0] # k1, k2, p1, p2, xi

  camera_tracking_margin: 20    # Important that edge keypoints are discarded!
  extrinsic:
    q_bc: [ 0.5, 0.5, 0.5, 0.5 ] # x y z w
    p_bc: [ 0.3, 0.000, 0.0 ] # x y z [m]
  noise: [
    1.0, 0.0,
    0.0, 1.0] # [pixel^2]