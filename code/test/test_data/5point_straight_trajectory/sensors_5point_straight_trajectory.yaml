imu:
  # inertial sensor noise model parameters (static)
  gyroscope_noise_density: 1.6968e-4       # [ rad / s / sqrt(Hz) ]
  gyroscope_random_walk: 1.9393e-5       # [ rad / s^2 / sqrt(Hz) ]
  accelerometer_noise_density: 0.002    # [ m / s^2 / sqrt(Hz) ]
  accelerometer_random_walk: 0.003    # [ m / s^3 / sqrt(Hz) ]

cam0:
  do_image_rectification: true
  resolution: [752, 480]        # resolution of camera
  camera_model: pinhole         # camera model
  distortion_model: radtan      # distortion model
  intrinsics: [458.654, 457.296, 367.215, 248.375] # fu, fv, cu, cv
  ds_intrinsics: [0.0, 0.0]     # xi, alpha
  camera_tracking_margin: 20    # Important that edge keypoints are discarded!
  distortion_coeffs: [-0.28340811, 0.07395907, 0.00019359, 1.76187114e-05] # k1, k2, p1, p2
  extrinsic:
    q_bc: [ -7.7071797555374275e-03, 1.0499323370587278e-02, 7.0175280029197162e-01, 7.1230146066895372e-01 ] # x y z w
    p_bc: [ -0.0216401454975, -0.064676986768, 0.00981073058949 ] # x y z [m]
  noise: [
    1.0, 0.0,
    0.0, 1.0] # [pixel^2]
