import cv2
import numpy as np

# Create a blank image with a black background
width, height = 150, 100
image = np.zeros((height, width, 3), np.uint8)

# Define the camera matrix
focal_length = 100
center = np.array([width / 2, height / 2])
camera_matrix = np.array([[focal_length, 0, center[0]],
                          [0, focal_length, center[1]],
                          [0, 0, 1]], dtype="double")

P = np.array([[1, 0, 0, 0], [0, 1, 0, 0], [0, 0, 1, 0]])
print(camera_matrix @ P)

# Create a 3D point in the world space
depth = 10
world_points = np.array([[0, 0, depth], [1, 1, depth], [-1, -1, depth], [1, -1, depth], [-1, 1, depth]], dtype='double')

# Project the 3D point onto the image plane
projected_points, _ = cv2.projectPoints(world_points, np.zeros((3, 1)), np.zeros((3, 1)), camera_matrix, None)

projected_points = projected_points.squeeze(1)
np.savetxt(str(depth) + ".txt", projected_points)

# Draw the projected point on the image
cv2.circle(image, tuple(np.squeeze(projected_points[0]).astype(int)), 5, (0, 255, 0), -1)
cv2.circle(image, tuple(np.squeeze(projected_points[1]).astype(int)), 5, (0, 255, 0), -1)
cv2.circle(image, tuple(np.squeeze(projected_points[2]).astype(int)), 5, (0, 255, 0), -1)
cv2.circle(image, tuple(np.squeeze(projected_points[3]).astype(int)), 5, (0, 255, 0), -1)
cv2.circle(image, tuple(np.squeeze(projected_points[4]).astype(int)), 5, (0, 255, 0), -1)

cv2.imshow("Pinhole Camera", image)
cv2.waitKey(0)
cv2.destroyAllWindows()
