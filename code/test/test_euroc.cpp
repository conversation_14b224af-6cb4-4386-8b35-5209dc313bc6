#include <gtest/gtest.h>

#include <chrono>

#include <Eigen/Geometry>
#include <rdvio/testing_util/integration/euroc_runner.hpp>

TEST(RdvioEurocTest, MH_01_easy) {
  // TODO (Vipul) This is not a proper test as the rd-vio system is not deterministic. Instead, we should calculate the
  // APE metrics using EVO tool and make a test out of it.
  const std::string calib_file{"/testroot/code/test/test_data/euroc/euroc.yaml"};
  const std::string config_file{"/testroot/code/test/test_data/euroc/setting.yaml"};
  const std::string euroc_data{"/testroot/data/mav0"};

  // In the pipeline we build the code four time because we need to build the debug and release versions for both the
  // single and multithreaded versions. Therefore, we need some way to get a unique trajectory file out of this test
  // otherwise we will only get the last one, so we get the current timestamp and append it to the filename.
  const int64_t timestamp{
      std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch())
          .count()};
  const std::string log_file{"/tmp/rdvio_euroc_trajectory_" + std::to_string(timestamp) + ".txt"};
  const std::string odometry_log_file{"/tmp/rdvio_euroc_odometry_" + std::to_string(timestamp) + ".txt"};

  const auto vio_estimate{
      rdvio::EurocRunner(calib_file, config_file, euroc_data, log_file, odometry_log_file, 25.0, 30.0)};

  // Set the expected pose of the vio system. (From AMD device)
  const Eigen::Quaterniond expected_quaternion(0.9933298, -0.1116757, -0.0286892, -0.0012073);  // w, x, y, z
  const Eigen::Vector3d expected_position(0.979025, 2.40904, 1.86573);
  const Eigen::Vector3d expected_linear_velocity(0.217064, 0.27492, 0.726479);
  const Eigen::Vector3d expected_angular_velocity(0.127374, 0.0249008, -0.0865319);

  EXPECT_TRUE(expected_quaternion.isApprox(vio_estimate.pose.q, 0.005))
      << "expected quaternion: " << expected_quaternion << " estimated quaternion: " << vio_estimate.pose.q;
  EXPECT_TRUE(expected_position.isApprox(vio_estimate.pose.p, 0.01))
      << "expected position: " << expected_position.array() << " estimated position: " << vio_estimate.pose.p.array();
  EXPECT_TRUE(expected_linear_velocity.isApprox(vio_estimate.twist.linear_velocity, 0.02))
      << "expected linear velocity: " << expected_linear_velocity
      << " estimated linear velocity: " << vio_estimate.twist.linear_velocity;
  EXPECT_TRUE(expected_angular_velocity.isApprox(vio_estimate.twist.angular_velocity, 0.01))
      << "expected angular velocity: " << expected_angular_velocity.array()
      << " estimated angular velocity: " << vio_estimate.twist.angular_velocity.array();
}