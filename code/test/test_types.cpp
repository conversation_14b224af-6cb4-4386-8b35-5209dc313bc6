#include <gtest/gtest.h>

#include <rdvio/types.h>

TEST(TypesTest, SystemStateToString) {
  ASSERT_EQ("INITIALIZING", rdvio::ToString(rdvio::SystemState::INITIALIZING));
  ASSERT_EQ("READY_TO_TRACK", rdvio::ToString(rdvio::SystemState::READY_TO_TRACK));
  ASSERT_EQ("TRACKING", rdvio::ToString(rdvio::SystemState::TRACKING));
  ASSERT_EQ("UNKNOWN", rdvio::ToString(rdvio::SystemState::UNKNOWN));
}

TEST(TypesTest, CameraModelParserTest) {
  ASSERT_EQ(rdvio::CameraModel::DOUBLE_SPHERE, rdvio::ParseCameraModel("ds"));
  ASSERT_EQ(rdvio::CameraModel::RADTAN, rdvio::ParseCameraModel("radtan"));
  ASSERT_EQ(rdvio::CameraModel::EQUI, rdvio::ParseCameraModel("equi"));
  ASSERT_EQ(rdvio::CameraModel::PINHOLE, rdvio::ParseCameraModel("none"));
}
