## library
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_POSITION_INDEPENDENT_CODE ON)

add_library(${PROJECT_NAME} SHARED "")

target_include_directories(${PROJECT_NAME}
        PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
        $<INSTALL_INTERFACE:include>
        ${SPL_CAMERA_MODELS_INCLUDE_DIR}
)

target_link_libraries(${PROJECT_NAME}
        PUBLIC
        fmt
        Threads::Threads
        ${OpenCV_LIBS}
        Eigen3::Eigen
        Ceres::ceres
        GLEW
        yaml-cpp
        ${SPL_CAMERA_MODELS_LIBRARIES}
        spl-logging::spl-logging
)

## sub directories
add_subdirectory(rdvio)
add_subdirectory(rdvio_estimation)
add_subdirectory(rdvio_extra)
add_subdirectory(rdvio_geometry)
add_subdirectory(rdvio_initialization)
add_subdirectory(rdvio_map)
add_subdirectory(rdvio_util)
add_subdirectory(rdvio_testing_util)

set(CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/cmake")
include(InstallingGeneral)

if (PACKAGE_LIBRARY)
    include(InstallingConfigs)
    include(Packaging)
else ()
    # Note(vkallenbach): This is required in order to build with plain CMAKE or colcon
    configure_file(
            ${CMAKE_CURRENT_SOURCE_DIR}/cmake/ColconConfig.cmake.in
            ${CMAKE_CURRENT_BINARY_DIR}/spl-rd-vioConfig.cmake
            @ONLY
    )
    install(FILES
            ${CMAKE_CURRENT_BINARY_DIR}/spl-rd-vioConfig.cmake
            DESTINATION lib/cmake/spl-rd-vio
    )
endif (PACKAGE_LIBRARY)
