#include <gtest/gtest.h>

#include <rdvio/geometry/stereo.h>

using namespace rdvio;

class TriangulateFixture : public ::testing::Test {
 protected:
  // We consider the 10m frame the world coordinate origin.
  matrix<3, 4> P1_{{100, 0, 75, 0}, {0, 100, 50, 0}, {0, 0, 1, 0}};
  // Pose of the second camera (8m) in world coordinates after moving forward two meters
  matrix<3, 4> P2_{{100, 0, 75, 0}, {0, 100, 50, 0}, {0, 0, 1, -2}};
  // Pose of the third camera (6m) in world coordinates after moving forward four meters
  matrix<3, 4> P3_{{100, 0, 75, 0}, {0, 100, 50, 0}, {0, 0, 1, -4}};

  vector<3> point1_{85, 60, 1};      // projected pixel of point at 1,1,10
  vector<3> point2_{87.5, 62.5, 1};  // projected pixel of point at 1,1,8
  vector<3> point3_{91.7, 66.7, 1};  // projected pixel of point at 1,1,6
};

TEST_F(TriangulateFixture, TwoObservationVersion) {
  const vector<4> optimized_point{triangulate_point(P1_, P2_, point1_, point2_)};

  // We do not have scale, so we can only check the ratio of the known points world coordinates and the optimized value.
  const float gt_xz_ratio{1.0 / 10.0};
  const float gt_yz_ratio{gt_xz_ratio};  // The points are symmetric wrt x and y

  const double optimized_xz_ratio{optimized_point(0) / optimized_point(2)};
  const double optimized_yz_ratio{optimized_point(1) / optimized_point(2)};

  EXPECT_NEAR(gt_xz_ratio, optimized_xz_ratio, 0.01);
  EXPECT_NEAR(gt_yz_ratio, optimized_yz_ratio, 0.01);
}

TEST_F(TriangulateFixture, NCountObservationVersion) {
  const std::vector<matrix<3, 4>> Ps{P1_, P2_, P3_};
  const std::vector<vector<3>> points{point1_, point2_, point3_};
  const vector<4> optimized_point{triangulate_point(Ps, points)};

  const float gt_xz_ratio{1.0 / 10.0};
  const float gt_yz_ratio{gt_xz_ratio};

  const double optimized_xz_ratio{optimized_point(0) / optimized_point(2)};
  const double optimized_yz_ratio{optimized_point(1) / optimized_point(2)};

  EXPECT_NEAR(gt_xz_ratio, optimized_xz_ratio, 0.01);
  EXPECT_NEAR(gt_yz_ratio, optimized_yz_ratio, 0.01);
}