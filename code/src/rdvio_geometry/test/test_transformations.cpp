#include <gtest/gtest.h>

#include "rdvio/geometry/transformations.hpp"
#include "rdvio/types.h"

using namespace rdvio;

struct VelocityCase {
  vector<3> linear;
  vector<3> angular;
  vector<3> expected_linear;
  vector<3> expected_angular;
};

class VelocityTransformationsFixture : public ::testing::Test, public ::testing::WithParamInterface<VelocityCase> {
 protected:
  // Assuming Body is in NED and Camera in shifted to the front right (2m front, 1m right) w.r.t body and looking
  // forward.
  const matrix<4> T_body_cam{{0, 0, 1, 2}, {1, 0, 0, 1}, {0, 1, 0, 0}, {0, 0, 0, 1}};
  // Assuming Body is in NED and IMU in shifted to the back left (1m back, 2m left) w.r.t body and oriented in ENU.
  const matrix<4> T_body_imu{{0, 1, 0, -1}, {1, 0, 0, -2}, {0, 0, -1, 0}, {0, 0, 0, 1}};

  const matrix<4> T_imu_cam{{1, 0, 0, 3}, {0, 0, 1, 3}, {0, -1, 0, 0}, {0, 0, 0, 1}};
};

TEST_P(VelocityTransformationsFixture, TransformVelocityTest) {
  const auto& param{GetParam()};

  const auto estimated_linear_velocity{geometry::TransformLinearVelocity(T_body_cam, param.linear, param.angular)};
  const auto estimated_angular_velocity{geometry::TransformAngularVelocity(T_body_cam, param.angular)};

  std::cout << estimated_linear_velocity << std::endl;
  std::cout << param.expected_linear << std::endl;
  EXPECT_TRUE(estimated_linear_velocity.isApprox(param.expected_linear, 1e-6));
  EXPECT_TRUE(estimated_angular_velocity.isApprox(param.expected_angular, 1e-6));
}

// Test linear velocity of cam frame given the velocity of the body frame.
INSTANTIATE_TEST_SUITE_P(
    VelocityTransformationScenarios, VelocityTransformationsFixture,
    ::testing::Values(
        // body moving along x-axis at 1 m/s.
        VelocityCase{vector<3>{1, 0, 0}, vector<3>::Zero(), vector<3>{0, 0, 1}, vector<3>::Zero()},
        // body moving along z-axis at 1 m/s.
        VelocityCase{vector<3>{0, 0, 1}, vector<3>::Zero(), vector<3>{0, 1, 0}, vector<3>::Zero()},
        // body rotation around z-axis at 1 rad/s.
        VelocityCase{vector<3>::Zero(), vector<3>{0, 0, 1}, vector<3>{2, 0, -1}, vector<3>{0, 1, 0}},
        // body rotation around y-axis at 1 rad/s and along x-axis with 3m/s.
        VelocityCase{vector<3>{3, 0, 0}, vector<3>{0, 1, 0}, vector<3>{0, -2, 3}, vector<3>{1, 0, 0}}));
