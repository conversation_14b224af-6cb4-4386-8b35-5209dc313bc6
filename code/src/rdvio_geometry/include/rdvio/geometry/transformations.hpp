#pragma once

#include "rdvio/types.h"

namespace rdvio::geometry {

matrix<3> ConvertToSkewSymmetric(const vector<3>& vec);

/**
 * It transforms the linear velocity from reference frame A to reference frame B assuming both frames are connected to a
 * rigid body.
 *
 * @param transformation A 4x4 transformation matrix that can transform a point from frame B to frame A. Denoted as
 * T_a_b
 * @param linear_velocity A 3x1 linear velocity vector represented in frame A.
 * @param angular_velocity A 3x1 angular velocity vector represented in frame A.
 */
vector<3> TransformLinearVelocity(const matrix<4>& transformation, const vector<3>& linear_velocity,
                                  const vector<3>& angular_velocity);

/**
 * It transforms the angular velocity from reference frame A to reference frame B assuming both frames are connected to
 * a rigid body.
 *
 * @param transformation A 4x4 transformation matrix that can transform a point from frame B to frame A. Denoted as
 * T_a_b
 * @param angular_velocity A 3x1 angular velocity vector represented in frame A.
 */
vector<3> TransformAngularVelocity(const matrix<4>& transformation, const vector<3>& angular_velocity);

Twist TransformTwist(const matrix<4>& transformation, const Twist& twist);

};  // namespace rdvio::geometry