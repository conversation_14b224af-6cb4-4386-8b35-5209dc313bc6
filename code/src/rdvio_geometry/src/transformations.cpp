#include "rdvio/geometry/transformations.hpp"

#include "rdvio/types.h"

namespace rdvio::geometry {

matrix<3> ConvertToSkewSymmetric(const vector<3>& vec) {
  return matrix<3>{{0, -vec.z(), vec.y()}, {vec.z(), 0, -vec.x()}, {-vec.y(), vec.x(), 0}};
}

vector<3> TransformLinearVelocity(const matrix<4>& transformation, const vector<3>& linear_velocity,
                                  const vector<3>& angular_velocity) {
  const auto R_a_b{transformation.block<3, 3>(0, 0)};
  const auto t_a_b{transformation.block<3, 1>(0, 3)};

  return R_a_b.transpose() * (linear_velocity + ConvertToSkewSymmetric(angular_velocity) * t_a_b);
}

vector<3> TransformAngularVelocity(const matrix<4>& transformation, const vector<3>& angular_velocity) {
  const auto R_a_b{transformation.block<3, 3>(0, 0)};

  return R_a_b.transpose() * angular_velocity;
}

// cppcheck-suppress unusedFunction
Twist TransformTwist(const matrix<4>& transformation, const Twist& twist) {
  const auto transformed_linear_velocity{
      TransformLinearVelocity(transformation, twist.linear_velocity, twist.angular_velocity)};
  const auto transformed_angular_velocity{TransformAngularVelocity(transformation, twist.angular_velocity)};

  return Twist{transformed_linear_velocity, transformed_angular_velocity};
}

};  // namespace rdvio::geometry