#include "rdvio/map/track.h"

#include "rdvio/estimation/solver.h"
#include "rdvio/geometry/stereo.h"

namespace rdvio {

Track::Track() { tag(TT_STATIC) = true; }
Track::~Track() = default;

const vector<3>& Track::GetKeypoint(Frame* frame) const { return frame->GetBearing(keypoint_refs.at(frame)); }

void Track::AddKeypoint(Frame* frame, size_t keypoint_index) {
  keypoint_refs[frame] = keypoint_index;
  frame->tracks[keypoint_index] = this;
  frame->reprojection_error_factors[keypoint_index] = Solver::CreateReprojectionErrorFactor(frame, this);
  if (this->tag(TT_TRIANGULATED)) {
    m_life++;
  } else {
    m_life = 1;
  }
}

void Track::RemoveKeypoint(Frame* frame, bool suicide_if_empty) {
  size_t keypoint_index = keypoint_refs.at(frame);
  std::optional<vector<3>> landmark;
  if (frame == FirstFrame()) {
    landmark = GetLandmarkPoint();
  }
  frame->tracks[keypoint_index] = nullptr;
  frame->reprojection_error_factors[keypoint_index].reset();
  keypoint_refs.erase(frame);
  if (keypoint_refs.size() > 0) {
    if (landmark.has_value()) {
      SetLandmarkPoint(landmark.value());
    }
  } else {
    tag(TT_VALID) = false;
    if (suicide_if_empty) {
      map->recycle_track(this);
    }
  }
}

std::optional<vector<3>> Track::Triangulate() {
  std::vector<matrix<3, 4>> Ps;
  std::vector<vector<3>> ps;
  for (const auto& [frame, keypoint_index] : KeypointMap()) {
    matrix<3, 4> P;
    matrix<3, 3> R;
    vector<3> T;
    auto pose = frame->get_pose(frame->camera);
    R = pose.q.conjugate().matrix();
    T = -(R * pose.p);
    P << R, T;
    Ps.push_back(P);
    ps.push_back(frame->GetBearing(keypoint_index));
  }

  bool is_valid = true;
  vector<4> hlandmark = triangulate_point(Ps, ps);
  for (size_t i = 0; i < ps.size(); ++i) {
    vector<3> qi = Ps[i] * hlandmark;
    if (!(qi[2] * hlandmark[3] > 0)) {
      is_valid = false;
      break;
    }
  }
  if (is_valid) {
    m_life = 1;
    return hlandmark.hnormalized();
  } else {
    return std::nullopt;
  }
}

vector<3> Track::GetLandmarkPoint() const {
  const auto& [frame, keypoint_index] = FirstKeypoint();
  auto camera = frame->get_pose(frame->camera);
  return camera.q * frame->GetBearing(keypoint_index) / landmark.inv_depth + camera.p;
}

void Track::SetLandmarkPoint(const vector<3>& p) {
  const auto [frame, keypoint_index] = FirstKeypoint();
  auto camera = frame->get_pose(frame->camera);
  landmark.inv_depth = 1.0 / (camera.q.conjugate() * (p - camera.p)).norm();
}

}  // namespace rdvio
