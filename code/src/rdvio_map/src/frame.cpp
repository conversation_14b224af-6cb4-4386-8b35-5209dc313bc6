#include "rdvio/map/frame.h"

#include "rdvio/estimation/ceres/reprojection_factor.h"
#include "rdvio/geometry/stereo.h"
#include "rdvio/map/map.h"
#include "rdvio/map/track.h"
#include "rdvio/util.hpp"
#include "rdvio/util/poisson_disk_filter.h"

namespace rdvio {

struct Frame::construct_by_frame_t {};

Frame::Frame() : map(nullptr) {}

Frame::Frame(const Frame& frame, const construct_by_frame_t& construct_by_frame)
    : Tagged(frame), Identifiable(frame), map(nullptr) {}

Frame::~Frame() = default;

std::unique_ptr<Frame> Frame::clone() const {
  std::unique_ptr<Frame> frame = std::make_unique<Frame>(*this, construct_by_frame_t());
  frame->K = K;
  frame->sqrt_inv_cov = sqrt_inv_cov;
  frame->image = image;
  frame->pose = pose;
  frame->motion = motion;
  frame->camera = camera;
  frame->imu = imu;
  frame->preintegration = preintegration;
  frame->features = features;
  frame->tracks = std::vector<Track*>(features.size(), nullptr);
  frame->reprojection_error_factors = std::vector<std::unique_ptr<ReprojectionErrorFactor>>(features.size());
  frame->map = nullptr;
  frame->speed_prior = speed_prior;
  return frame;
}

void Frame::append_keypoint(const Feature& feature) {
  features.emplace_back(feature);
  tracks.emplace_back(nullptr);
  reprojection_error_factors.emplace_back(nullptr);
}

Track* Frame::get_track(size_t feature_index, Map* allocation_map) {
  if (!allocation_map) {
    allocation_map = map;
  }
  if (tracks[feature_index] == nullptr) {
    Track* track = allocation_map->create_track();
    track->AddKeypoint(this, feature_index);
  }
  return tracks[feature_index];
}

void Frame::detect_keypoints(const FeatureTrackerConfig& config) {
  constexpr auto logger_name{"Frame::detect_keypoints"};
  std::vector<vector<2>> pkeypoints(features.size());
  for (size_t i = 0; i < features.size(); ++i) {
    pkeypoints[i] = PinholeProject(features[i].bearing, K);
  }

  image->DetectKeypoints(config.max_keypoint_detection, config.min_keypoint_distance, pkeypoints);

  size_t old_keypoint_num = features.size();
  features.resize(pkeypoints.size());
  tracks.resize(pkeypoints.size(), nullptr);
  reprojection_error_factors.resize(pkeypoints.size());
  for (size_t i = old_keypoint_num; i < pkeypoints.size(); ++i) {
    features[i].bearing = PinholeUnproject(pkeypoints[i], K);
  }

  utils::LogStatistics(logger_name, "Frame id {}, detected {} new keypoints, total: {}", id(),
                       pkeypoints.size() - old_keypoint_num, pkeypoints.size());
}

void Frame::track_keypoints(Frame* next_frame, const FeatureTrackerConfig& config) {
  constexpr auto logger_name{"Frame::track_keypoints"};
  utils::LogStatistics(logger_name, "Processing frame id {}, timestamp: {}", id(), image->t);
  std::vector<vector<2>> curr_keypoints(features.size());
  std::vector<vector<2>> next_keypoints;

  for (size_t i = 0; i < features.size(); ++i) {
    curr_keypoints[i] = PinholeProject(features[i].bearing, K);
  }

  if (config.predict_keypoints) {
    quaternion delta_key_q =
        (camera.Q_body_sensor.conjugate() * imu.Q_body_sensor * next_frame->preintegration.delta.q *
         next_frame->imu.Q_body_sensor.conjugate() * next_frame->camera.Q_body_sensor)
            .conjugate();
    next_keypoints.resize(curr_keypoints.size());
    for (size_t i = 0; i < features.size(); ++i) {
      next_keypoints[i] = PinholeProject(delta_key_q * features[i].bearing, next_frame->K);
    }
  }

  std::vector<char> status, mask;
  image->TrackKeypoints(next_frame->image.get(), curr_keypoints, next_keypoints, status);

  utils::LogStatistics(logger_name, "Optical flow tracking complete, tracked {} keypoints out off {}",
                       next_keypoints.size(), curr_keypoints.size());

  // only reserved for find essential matrix
  std::vector<vector<2>> curr_keypoints_h, next_keypoints_h;
  std::vector<vector<3>> next_bearings;
  for (size_t i = 0; i < curr_keypoints.size(); ++i) {
    curr_keypoints_h.push_back(features[i].bearing.hnormalized());
    const vector<3> next_bearing{PinholeUnproject(next_keypoints[i], next_frame->K)};
    next_keypoints_h.push_back(next_bearing.hnormalized());
    next_bearings.push_back(next_bearing);
  }

  // WARN(victor, jack, vipul): "mask" can produce segfaults, as
  // find_essential_matrix seems to not to safely manipulate / change "mask" here.
  // This results in a unexpected length for the vector "mask", hence the safeguarding.
  matrix<3> E = find_essential_matrix(curr_keypoints_h, next_keypoints_h, mask, 1.0);
  for (size_t i = 0; i < status.size(); ++i) {
    if (i < mask.size()) {
      if (not mask[i]) {
        status[i] = 0;
      }
    }
  }

  const size_t statistics_essential_matrix_filtered_keypoint{
      static_cast<size_t>(std::count(status.begin(), status.end(), 1))};
  utils::LogStatistics(logger_name, "After essential matrix filtering: {} keypoints removed and {} keypoints left",
                       curr_keypoints.size() - statistics_essential_matrix_filtered_keypoint,
                       statistics_essential_matrix_filtered_keypoint);
  matrix<3> R = find_rotation_matrix(ExtractBearingsFromFeatures(features), next_bearings, mask,
                                     (M_PI / 180.0) * config.rotation_ransac_threshold);

  std::vector<double> angles;
  for (size_t i = 0; i < mask.size(); ++i) {
    if (mask[i]) {
      double angle = acos((R * features[i].bearing).dot(next_bearings[i]));
      angles.emplace_back(angle * 180 / M_PI);
    }
  }
  std::sort(angles.begin(), angles.end());
  double misalignment = angles.size() > 0 ? angles[static_cast<size_t>(angles.size() * config.angle_percentile)] : 0;

  if (misalignment < config.rotation_misalignment_threshold) {
    spl::logging::Warning(logger_name, "Detected no translation movement (misalignment: {} < threshold: {})",
                          misalignment, config.rotation_misalignment_threshold);
    next_frame->tag(FT_NO_TRANSLATION) = true;
  }

  // filter keypoints based on track length
  std::vector<std::pair<size_t, size_t>> keypoint_index_track_length;
  keypoint_index_track_length.reserve(curr_keypoints.size());
  for (size_t i = 0; i < curr_keypoints.size(); ++i) {
    if (status[i] == 0) {
      continue;
    }
    Track* track = get_track(i);
    if (track == nullptr) {
      continue;
    }
    keypoint_index_track_length.emplace_back(i, track->TrackLength());
  }

  std::sort(keypoint_index_track_length.begin(), keypoint_index_track_length.end(),
            [](const auto& a, const auto& b) { return a.second > b.second; });

  PoissonDiskFilter<2> filter(config.min_keypoint_distance);
  size_t statistics_filtered_count{0};
  for (auto& [keypoint_index, track_length] : keypoint_index_track_length) {
    vector<2> pt = next_keypoints[keypoint_index];
    Track* track = this->get_track(keypoint_index);
    if (filter.permit_point(pt) and (not track || not track->tag(TT_TRASH))) {
      filter.preset_point(pt);
    } else {
      status[keypoint_index] = 0;
      statistics_filtered_count++;
    }
  }
  utils::LogStatistics(logger_name, "Poisson filtering removed {} keypoints", statistics_filtered_count);

  size_t statistics_added_keypoint_count{0};
  for (size_t curr_keypoint_index = 0; curr_keypoint_index < curr_keypoints.size(); ++curr_keypoint_index) {
    if (status[curr_keypoint_index]) {
      size_t next_keypoint_index = next_frame->keypoint_num();
      next_frame->append_keypoint({next_bearings[curr_keypoint_index]});
      get_track(curr_keypoint_index, nullptr)->AddKeypoint(next_frame, next_keypoint_index);
      statistics_added_keypoint_count++;
    }
  }

  utils::LogStatistics(logger_name, "Added {} keypoints to frame ID: {}", statistics_added_keypoint_count,
                       next_frame->id());
}

PoseState Frame::get_pose(const ExtrinsicParams& sensor) const {
  PoseState result;
  result.q = pose.q * sensor.Q_body_sensor;
  result.p = pose.p + pose.q * sensor.P_body_sensor;
  return result;
}

void Frame::set_pose(const ExtrinsicParams& sensor, const PoseState& pose) {
  this->pose.q = pose.q * sensor.Q_body_sensor.conjugate();
  this->pose.p = pose.p - this->pose.q * sensor.P_body_sensor;
}

std::unique_lock<std::mutex> Frame::lock() const {
  if (map) {
    return map->lock();
  } else {
    return {};
  }
}

std::unique_ptr<Frame> MakeFrameFromConfig(const CameraConfig& camera_config, const ImuConfig& imu_config,
                                           const std::shared_ptr<extra::ImageFrontend> image,
                                           std::optional<const double> speed) {
  std::unique_ptr<Frame> frame = std::make_unique<Frame>();
  frame->K = camera_config.K;
  frame->image = std::move(image);
  frame->sqrt_inv_cov = frame->K.block<2, 2>(0, 0);
  frame->sqrt_inv_cov(0, 0) /= ::sqrt(camera_config.keypoint_noise_cov(0, 0));
  frame->sqrt_inv_cov(1, 1) /= ::sqrt(camera_config.keypoint_noise_cov(1, 1));
  frame->camera.Q_body_sensor = camera_config.camera_to_body_rotation;
  frame->camera.P_body_sensor = camera_config.camera_to_body_translation;
  frame->imu.Q_body_sensor = imu_config.imu_to_body_rotation;
  frame->imu.P_body_sensor = imu_config.imu_to_body_translation;
  frame->preintegration.cov_a = imu_config.accelerometer_noise_cov;
  frame->preintegration.cov_w = imu_config.gyroscope_noise_cov;
  frame->preintegration.cov_ba = imu_config.accelerometer_bias_noise_cov;
  frame->preintegration.cov_bg = imu_config.gyroscope_bias_noise_cov;

  if (speed.has_value()) {
    frame->speed_prior = speed.value();
  }
  return frame;
}

std::vector<vector<3>> ExtractBearingsFromFeatures(const std::vector<Feature>& features) {
  std::vector<vector<3>> bearings;
  bearings.reserve(std::size(features));

  std::transform(features.cbegin(), features.cend(), std::back_inserter(bearings),
                 [](const auto& feature) { return feature.bearing; });

  return bearings;
}

}  // namespace rdvio
