#include "rdvio/map/map.h"

#include "rdvio/estimation/ceres/marginalization_factor.h"
#include "rdvio/map/frame.h"
#include "rdvio/map/track.h"
#include "rdvio/util.hpp"

namespace rdvio {

struct Map::construct_by_map_t {};

Map::Map() = default;

Map::~Map() = default;

void Map::clear() {
  frames.clear();
  tracks.clear();
}

void Map::attach_frame(std::unique_ptr<Frame> frame, size_t position) {
  frame->map = this;
  if (position == nil()) {
    frames.emplace_back(std::move(frame));
  } else {
    frames.emplace(frames.begin() + position, std::move(frame));
  }
}

std::unique_ptr<Frame> Map::detach_frame(size_t index) {
  std::unique_ptr<Frame> frame = std::move(frames[index]);
  frames.erase(frames.begin() + index);
  frame->map = nullptr;
  return frame;
}

void Map::untrack_frame(Frame* frame) {
  size_t statistic_tracked_count{0};
  for (size_t i = 0; i < frame->keypoint_num(); ++i) {
    if (Track* const track = frame->get_track(i); track) {
      track->RemoveKeypoint(frame);
      ++statistic_tracked_count;
    }
  }
  utils::LogStatistics("Map::untrack_frame", "Frame ID: {}, has {} keypoints, {} keypoints untracked", frame->id(),
                       frame->keypoint_num(), statistic_tracked_count);
}

void Map::erase_frame(size_t index) {
  Frame* frame = frames[index].get();
  untrack_frame(frame);
  detach_frame(index);
}

// NOTE(Jack): The original implementation had an ID that you let you select which frame you wanted to marginalize, but
// it was hardcoded so that only the value of zero was allowed. This was confusing because it could make the user of the
// function think they had a choice, but in reality they only had one option. Therefore, we removed the option to
// eliminate the ambiguity.
void Map::marginalize_frame() {
  if (marginalization_factor) {
    marginalization_factor->marginalize(0);
  } else {
    throw std::runtime_error("Map::marginalize_frame() - marginalization_factor is not initialized yet");
  }

  const size_t statistic_frame_id{frames[0]->id()};
  const size_t statistic_frame_keypoint_num{frames[0]->keypoint_num()};
  size_t statistic_frame_track_num{0};
  Frame* const frame{frames[0].get()};
  for (size_t keypoint_idx{0}; keypoint_idx < frame->keypoint_num(); ++keypoint_idx) {
    if (Track* const track = frame->get_track(keypoint_idx); track) {
      track->RemoveKeypoint(frame);
      ++statistic_frame_track_num;
    }
  }
  frames.erase(frames.begin());
  utils::LogStatistics(
      "Map::marginalize_frame",
      "Frame ID: {} with {} keypoints and {} tracks has been marginalized successfully - total frames now: {}",
      statistic_frame_id, statistic_frame_keypoint_num, statistic_frame_track_num, frames.size());
}

size_t Map::frame_index_by_id(size_t id) const {
  struct FrameID {
    // cppcheck-suppress noExplicitConstructor
    FrameID(const std::unique_ptr<Frame>& frame) : id(frame->id()) {}
    // cppcheck-suppress noExplicitConstructor
    FrameID(size_t id) : id(id) {}
    bool operator<(const FrameID& fi) const { return id < fi.id; }
    size_t id;
  };
  const auto it{std::lower_bound(frames.cbegin(), frames.cend(), id, std::less<FrameID>())};
  if (it == frames.cend()) {
    return nil();
  }
  if (id < (*it)->id()) {
    return nil();
  }
  return std::distance(frames.cbegin(), it);
}

Track* Map::create_track() {
  std::unique_ptr<Track> track = std::make_unique<Track>(construct_by_map_t());
  track->map_index = tracks.size();
  track->map = this;
  track_id_map[track->id()] = track.get();
  return tracks.emplace_back(std::move(track)).get();
}

void Map::erase_track(Track* track) {
  while (track->TrackLength() > 0) {
    track->RemoveKeypoint(track->KeypointMap().begin()->first, false);
  }
  recycle_track(track);
}

void Map::prune_tracks(const std::function<bool(const Track*)>& condition) {
  std::vector<Track*> tracks_to_prune;
  for (size_t i = 0; i < track_num(); ++i) {
    if (Track* track = get_track(i); condition(track)) {
      tracks_to_prune.push_back(track);
    }
  }
  for (const auto& track : tracks_to_prune) {
    erase_track(track);
  }
}

void Map::recycle_track(Track* track) {
  if (track->map_index != tracks.back()->map_index) {
    tracks[track->map_index].swap(tracks.back());
    tracks[track->map_index]->map_index = track->map_index;
  }
  track_id_map.erase(track->id());
  tracks.pop_back();
}

}  // namespace rdvio
