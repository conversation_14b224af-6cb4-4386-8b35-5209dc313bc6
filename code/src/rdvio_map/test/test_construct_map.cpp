#include <gtest/gtest.h>

#include <rdvio/map/frame.h>
#include <rdvio/map/map.h>
#include <rdvio/map/track.h>
#include <rdvio/testing_util/unit/construct_map_fixture.hpp>

using namespace rdvio;

TEST_F(ConstructMapFixture, TestConstructedMapSize) {
  EXPECT_EQ(map_->frame_num(), 3);
  EXPECT_EQ(map_->track_num(), 5);

  const Frame* const frame_10m{map_->get_frame(0)};
  EXPECT_EQ(frame_10m->keypoint_num(), 5);

  const Track* const track_0{map_->get_track(0)};
  EXPECT_EQ(track_0->TrackLength(), 3);
}
