#pragma once

#include "rdvio/config.hpp"
#include "rdvio/estimation/preintegrator.h"
#include "rdvio/estimation/state.h"
#include "rdvio/extra/image_frontend.hpp"
#include "rdvio/types.h"
#include "rdvio/util/identifiable.h"
#include "rdvio/util/tag.h"

namespace rdvio {

class Config;
class Track;
class Map;
class ReprojectionErrorFactor;

enum FrameTag { FT_KEYFRAME = 0, FT_NO_TRANSLATION, FT_FIX_POSE, FT_FIX_MOTION };

// TODO (aabouee,vkallenbach,vgarg): Move frame id to the frame class instead of image object
class Frame : public Tagged<FrameTag>, public Identifiable<Frame> {
  friend class Track;
  friend class Map;
  struct construct_by_frame_t;
  Map* map;

 public:
  Frame();
  Frame(const Frame& frame, const construct_by_frame_t& construct);
  virtual ~Frame();

  std::unique_ptr<Frame> clone() const;

  size_t keypoint_num() const { return features.size(); }

  void append_keypoint(const Feature& feature);

  const vector<3>& GetBearing(size_t keypoint_index) const {
    assert(abs(features[keypoint_index].bearing.norm() - 1.0) <= 0.001 && "bearing vector is not normalized");

    return features[keypoint_index].bearing;
  }

  Track* get_track(size_t keypoint_index) const { return tracks[keypoint_index]; }

  Track* get_track(size_t keypoint_index, Map* allocation_map);

  void detect_keypoints(const FeatureTrackerConfig& config);
  void track_keypoints(Frame* next_frame, const FeatureTrackerConfig& config);

  PoseState get_pose(const ExtrinsicParams& sensor) const;
  void set_pose(const ExtrinsicParams& sensor, const PoseState& pose);

  bool has_map() const { return map != nullptr; }

  std::unique_lock<std::mutex> lock() const;

  matrix<3> K;
  matrix<2> sqrt_inv_cov;
  std::shared_ptr<extra::ImageFrontend> image;

  PoseState pose;
  MotionState motion;
  ExtrinsicParams camera;
  ExtrinsicParams imu;
  double speed_prior{-1.0};  // This is the "prior" for solving SfM and BA etc.

  PreIntegrator preintegration;
  PreIntegrator keyframe_preintegration;

  std::vector<std::unique_ptr<Frame>> subframes;
  std::vector<std::unique_ptr<ReprojectionErrorFactor>> reprojection_error_factors;

 private:
  std::vector<Feature> features;
  std::vector<Track*> tracks;
};

std::unique_ptr<Frame> MakeFrameFromConfig(const CameraConfig& camera_config, const ImuConfig& imu_config,
                                           const std::shared_ptr<extra::ImageFrontend> image,
                                           std::optional<const double> speed = std::nullopt);

std::vector<vector<3>> ExtractBearingsFromFeatures(const std::vector<Feature>& features);

}  // namespace rdvio
