#pragma once
#include <optional>

#include <rdvio/estimation/state.h>
#include <rdvio/map/frame.h>
#include <rdvio/map/map.h>
#include <rdvio/types.h>
#include <rdvio/util/identifiable.h>
#include <rdvio/util/tag.h>

namespace rdvio {

enum TrackTag { TT_VALID = 0, TT_TRIANGULATED, TT_FIX_INVD, TT_TRASH, TT_STATIC, TT_OUTLIER, TT_TEMP };

class Track : public Tagged<TrackTag>, public Identifiable<Track> {
  friend class Map;
  size_t map_index;
  Map* map;
  Track();

 public:
  Track(const Map::construct_by_map_t&) : Track() {}

  virtual ~Track();

  size_t TrackLength() const { return keypoint_refs.size(); }

  std::pair<Frame*, size_t> FirstKeypoint() const { return *keypoint_refs.begin(); }

  std::pair<Frame*, size_t> LastKeypoint() const { return *keypoint_refs.rbegin(); }

  Frame* FirstFrame() const { return keypoint_refs.begin()->first; }

  Frame* LastFrame() const { return keypoint_refs.rbegin()->first; }

  const std::map<Frame*, size_t, compare<Frame*>>& KeypointMap() const { return keypoint_refs; }

  bool HasKeypoint(const Frame* const frame) const {
    // NOTE(Jack): See note in Track::GetKeypointIndex() about const cast
    return keypoint_refs.count(const_cast<Frame*>(frame)) > 0;
  }

  size_t GetKeypointIndex(const Frame* const frame) const {
    // NOTE(Jack): const_cast should not be used lightly! In this case however, by using const_cast here, we allow
    // massive sections of other pieces of code to use const properly. Considering that both the `.at()` method
    // below, and the `.count()` method above do not risk editing the frames in the map, it is an acceptable
    // tradeoff to use const_cast, because it lets other sections maintain const correctness, where actual problems
    // could happen. Furthermore, the code as it was before already was const unsafe - so it is directly here
    // neither a plus or negative.
    if (HasKeypoint(frame)) {
      return keypoint_refs.at(const_cast<Frame*>(frame));
    } else {
      return nil();
    }
  }

  const vector<3>& GetKeypoint(Frame* frame) const;
  void AddKeypoint(Frame* frame, size_t keypoint_index);
  void RemoveKeypoint(Frame* frame, bool suicide_if_empty = true);

  std::optional<vector<3>> Triangulate();

  vector<3> GetLandmarkPoint() const;
  void SetLandmarkPoint(const vector<3>& p);

  std::unique_lock<std::mutex> Lock() const { return map->lock(); }

  LandmarkState landmark;
  size_t m_life = 0;

 private:
  std::map<Frame*, size_t, compare<Frame*>> keypoint_refs;
};

}  // namespace rdvio
