#pragma once

#include <rdvio/types.h>

// --- Nots on the parsac (normal and IMU) framework (<PERSON>) ---
//
// If you have never worked with RANSAC related algorithms before, it will take you a while to understand the parsac
// code and the unit testing.
//
// The basic idea is that we have a way to generate hypothesis given a set of input data (SinFitSolver), and that we can
// check if this model is a good fit to the data (SinFitEvaluator). That is the basic idea behind RANSAC and its related
// algorithms.
//
// What will you help understand the implementation be the original authors, is the idea that they tried to use C++
// features (ex. templates) to make the code generic, but failed. For example in IMU_parsac they have a template
// parameter ModelType, but then hardcode the return value in several places to be matrix<4>... A classic example of
// making code "generic", for a world that does not even exist, and actually just making the code much more complicated!
// If you are reading this code and are scared by the complexity, please just consider if you can remove the complicated
// looking part nad still have everything work fine. In many places you will find this is the case.

namespace rdvio::parsac_testing {

// If they had properly templated the code, then we could have used a much more simple ModelType. Unfortunately, in
// order to use the same code for testing both Parsac and IMU_parsac, we have to use the ModelType required by
// IMU_parsac, which is a 4x4 matrix. If the code was properly template, we could have just used a double because all
// our model is simply a sin amplitude.
matrix<4> DummyModel(const double amplitude) {
  matrix<4> model;
  model(0, 0) = amplitude;

  return model;
}

double SinFit(const double amplitude, const double x, const double y) {
  return std::abs((amplitude * std::sin(x)) - y);
}

// To keep complexity low I hardcode the models that we evaluate in the tests. The tests are designed such that the
// model with amplitude=2 is the correct model.
struct SinFitSolver {
  std::vector<matrix<4>> operator()(const std::array<double, 1>& samples1,
                                    const std::array<vector<2>, 1>& samples2) const {
    static_cast<void>(samples1);
    static_cast<void>(samples2);

    return {DummyModel(0.0), DummyModel(1.0), DummyModel(2.0), DummyModel(3.0), DummyModel(4.0)};
  }
};

struct SinFitEvaluator {
  explicit SinFitEvaluator(const matrix<4> _amplitude) : amplitude_{_amplitude} {}

  double operator()(const double x, const vector<2> y) const { return SinFit(amplitude_(0, 0), x, y(0)); }

  matrix<4> amplitude_;
};

std::tuple<std::vector<double>, std::vector<vector<2>>> GenerateSinWave(const size_t num_points,
                                                                        const double amplitude) {
  std::vector<double> x;
  std::vector<vector<2>> y;
  for (size_t i{0}; i < num_points; ++i) {
    x.push_back(static_cast<double>(i));

    const double y_i{amplitude * std::sin(i)};
    y.push_back({y_i, 0.0});
  }

  return {x, y};
}

}  // namespace rdvio::parsac_testing