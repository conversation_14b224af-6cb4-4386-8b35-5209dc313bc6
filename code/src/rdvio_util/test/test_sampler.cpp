#include <gtest/gtest.h>

#include <rdvio/util/sampler.h>

using namespace rdvio;

TEST(TestSampler, TestIsSampledLogic) {
  std::vector<float> random_input_data{0, 0.1, 0.2, 0.3, 0.4};
  Sampler sampler{random_input_data};

  for (size_t idx{0}; idx < std::size(random_input_data); idx++) {
    EXPECT_FALSE(sampler.IsSampled(idx));
  }

  // NOTE(Jack): If we draw more than five times then I think we end up in a infinite loop. This is not necesarily a
  // problem, but I want you to understand why I had to choose n < 5 here. To actually understand why this happens go
  // read the implementation of Sample::DrawByWeight
  std::array<size_t, 5> gt_draws{4, 3, 1, 2, 0};  // Seed is fixed so draws should always be same order
  for (size_t n{0}; n < 5; n++) {
    const size_t draw_n{sampler.DrawByWeight()};

    EXPECT_EQ(draw_n, gt_draws[n]);
  }

  for (size_t idx{0}; idx < std::size(random_input_data); idx++) {
    EXPECT_TRUE(sampler.IsSampled(idx));
  }
}