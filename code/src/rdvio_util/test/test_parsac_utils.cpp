#include <gtest/gtest.h>

#include "rdvio/util/parsac_utils.h"

using namespace rdvio;

TEST(TestParsacUtils, TestClamping) {
  constexpr double bin_size{1.0};
  constexpr size_t n_bins{10};

  struct TestCase {
    double coord;
    double norm;
    size_t expected;
  };

  const std::array<TestCase, 9> cases{{{-3.7, 0.0, 0},
                                       {0.0, 0.0, 0},
                                       {0.9, 0.0, 0},
                                       {3.1, 0.0, 3},
                                       {8.999, 0.0, 8},
                                       {12.0, 0.0, 9},
                                       {50.0, 0.0, 9},
                                       {-7.0, 5.0, 0},
                                       {12.0, 3.0, 9}}};

  for (const auto& _case : cases) {
    const auto idx{GetBinIndex(_case.coord, _case.norm, bin_size, n_bins)};
    EXPECT_EQ(idx, _case.expected);
  }
}