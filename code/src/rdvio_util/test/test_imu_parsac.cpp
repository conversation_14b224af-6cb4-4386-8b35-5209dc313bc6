#include <gtest/gtest.h>

#include <rdvio/util/imu_parsac.h>

using namespace rdvio;

#include "parsac_testing_utils.hpp"

TEST(TestImuParsac, ZZZ) {
  // There is a hard coded assertion that the imu parsac needs at least 20 points, otherwise it short circuits and
  // returns. Therefore, here we need at least 20 inliers to actually get the algorithm to evaluate and not short
  // circuit.
  const size_t num_inliers{21};
  const double amplitude{2.0};
  auto [x, y]{parsac_testing::GenerateSinWave(num_inliers, amplitude)};

  const std::vector<double> x_outliers{6.66, -6.66};
  const std::vector<vector<2>> y_outliers{{6.66, 0.0}, {-6.66, 0.0}};
  x.insert(std::end(x), std::cbegin(x_outliers), std::cend(x_outliers));
  y.insert(std::end(y), std::cbegin(y_outliers), std::cend(y_outliers));
  const size_t total_points{std::size(x)};

  // WARN(Jack): This is a bad initialization strategy! If these things are not set, then the imu parsac will not work
  // at all or segfault! These should be set in a constructor or factory.
  IMU_Parsac<1, matrix<4>, parsac_testing::SinFitSolver, parsac_testing::SinFitEvaluator> imu_parsac(1e-3);
  imu_parsac.SetLens(std::vector<size_t>(total_points, 1));
  imu_parsac.SetDynamicProbability(0.2);
  matrix<3> R;
  R(0, 0) = 2.0;
  vector<3> t;
  imu_parsac.SetPriorPose(R, t);

  static std::vector<float> bin_confidences(400, 0.5);
  const matrix<4> best_model{imu_parsac.solve(bin_confidences, x, y)};

  EXPECT_EQ(best_model(0, 0), amplitude);
  EXPECT_EQ(imu_parsac.inlier_count, num_inliers);
  const auto inlier_mask{imu_parsac.inlier_mask};
  for (size_t i{0}; i < num_inliers; ++i) {
    EXPECT_EQ(inlier_mask[i], 1);
  }

  EXPECT_EQ(inlier_mask[num_inliers], 0);
  EXPECT_EQ(inlier_mask[num_inliers + 1], 0);
}