#include <gtest/gtest.h>

#include <rdvio/util/parsac.h>

#include "parsac_testing_utils.hpp"

using namespace rdvio;

TEST(TestParsac, SinModelInlierOutlier) {
  const size_t num_inliers{10};
  const double amplitude{2.0};
  auto [x, y]{parsac_testing::GenerateSinWave(num_inliers, amplitude)};

  // Add two random points to make sure that not only the correct model is found, but also that outliers are rejected.
  const std::vector<double> x_outliers{6.66, -6.66};
  const std::vector<vector<2>> y_outliers{{6.66, 0.0}, {-6.66, 0.0}};
  x.insert(std::end(x), std::cbegin(x_outliers), std::cend(x_outliers));
  y.insert(std::end(y), std::cbegin(y_outliers), std::cend(y_outliers));

  // NOTE(Jack): We can have a very tight threshold here only because we created perfect fitting test data!
  Parsac<1, matrix<4>, parsac_testing::SinFitSolver, parsac_testing::SinFitEvaluator> parsac(1e-3);
  static std::vector<float> bin_confidences(400, 0.5);
  const matrix<4> best_model{parsac.solve(bin_confidences, x, y)};

  EXPECT_EQ(best_model(0, 0), amplitude);
  EXPECT_EQ(parsac.inlier_count, num_inliers);
  const auto inlier_mask{parsac.inlier_mask};
  for (size_t i{0}; i < num_inliers; ++i) {
    EXPECT_EQ(inlier_mask[i], 1);
  }

  // The outliers are properly detected
  EXPECT_EQ(inlier_mask[num_inliers], 0);
  EXPECT_EQ(inlier_mask[num_inliers + 1], 0);
}