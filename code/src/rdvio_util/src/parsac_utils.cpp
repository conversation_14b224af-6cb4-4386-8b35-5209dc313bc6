#include <rdvio/util/parsac_utils.h>

namespace rdvio {

// cppcheck-suppress unusedFunction
float ComputeScore(const size_t num_valid_bins, const std::vector<size_t>& valid_bin_data_sizes,
                   const std::vector<vector<2>>& bin_locations, const std::vector<size_t>& map_valid_bin_to_bin,
                   const std::vector<std::vector<size_t>>& valid_bin_inliers, std::vector<float>& valid_bin_confidences,
                   const bool do_weighting, const double m_dynamic_probability,
                   const std::vector<float>& m_validBinLens) {
  valid_bin_confidences.resize(num_valid_bins);

  float validBinConfidenceSum = 0, validBinConfidenceSqSum = 0;
  vector<2> sum(0.0, 0.0);

  for (size_t iBinValid = 0; iBinValid < num_valid_bins; ++iBinValid) {
    // Weighting is used by imu parsac but not the regular parsac - looks like a hyperparameter hack of sorts
    float t{1.0};
    if (do_weighting) {
      t = 1 - std::pow(m_dynamic_probability, 0.10 * m_validBinLens[iBinValid]);
    }
    const float validBinConfidence = t * float(valid_bin_inliers[iBinValid].size()) / valid_bin_data_sizes[iBinValid];
    valid_bin_confidences[iBinValid] = validBinConfidence;
    vector<2> x = bin_locations[map_valid_bin_to_bin[iBinValid]];
    x *= validBinConfidence;
    sum += x;
    validBinConfidenceSum += validBinConfidence;
    validBinConfidenceSqSum += validBinConfidence * validBinConfidence;
  }
  float norm = 1.f / validBinConfidenceSum;
  vector<2>& mean = sum;
  mean *= norm;

  float Cxx = 0, Cxy = 0, Cyy = 0;
  for (size_t iBinValid = 0; iBinValid < num_valid_bins; ++iBinValid) {
    const float validBinConfidence = valid_bin_confidences[iBinValid];
    vector<2> x = bin_locations[map_valid_bin_to_bin[iBinValid]];
    vector<2> dx(x[0] - mean[0], x[1] - mean[1]);
    Cxx += (dx[0] * dx[0]) * validBinConfidence;
    Cxy += (dx[0] * dx[1]) * validBinConfidence;
    Cyy += (dx[1] * dx[1]) * validBinConfidence;
  }

  norm = validBinConfidenceSum / (validBinConfidenceSum * validBinConfidenceSum - validBinConfidenceSqSum);
  float imgRatio = norm * sqrt(Cxx * Cyy - Cxy * Cxy);
  float score = imgRatio * validBinConfidenceSum;
  return score;
}

// cppcheck-suppress unusedFunction
void ConvertInliersListToValidBin(const size_t num_valid_bins, const std::vector<size_t>& map_data_to_valid_bin,
                                  const std::vector<char>& inliers_mask,
                                  std::vector<std::vector<size_t>>& valid_bin_inliers) {
  valid_bin_inliers = std::vector<std::vector<size_t>>(num_valid_bins, std::vector<size_t>());
  for (int iBinValid = 0; iBinValid < num_valid_bins; ++iBinValid) {
    valid_bin_inliers[iBinValid].resize(0);
  }

  const size_t N = inliers_mask.size();
  for (size_t i = 0; i < N; ++i) {
    if (inliers_mask[i] == 1) {
      valid_bin_inliers[map_data_to_valid_bin[i]].push_back(i);
    }
  }
}

// cppcheck-suppress unusedFunction
void ConvertConfidencesBinToValidBin(const size_t num_valid_bins, const std::vector<size_t>& map_valid_bin_to_bin,
                                     const std::vector<float>& bin_confidences,
                                     std::vector<float>& valid_bin_confidences) {
  valid_bin_confidences.resize(num_valid_bins);
  for (size_t iBin = 0; iBin < num_valid_bins; ++iBin) {
    valid_bin_confidences[iBin] = bin_confidences[map_valid_bin_to_bin[iBin]];
  }
}

// cppcheck-suppress unusedFunction
void ConvertConfidencesValidBinToBin(const size_t num_bins, const std::vector<size_t>& map_bin_to_valid_bin,
                                     const std::vector<float>& valid_bin_confidences,
                                     std::vector<float>& bin_confidences) {
  bin_confidences.resize(num_bins);
  for (size_t iBin = 0; iBin < num_bins; ++iBin) {
    if (map_bin_to_valid_bin[iBin] == SIZE_MAX) {
      bin_confidences[iBin] = 0;
    } else {
      bin_confidences[iBin] = valid_bin_confidences[map_bin_to_valid_bin[iBin]];
    }
  }
}

// cppcheck-suppress unusedFunction
void ThresholdAndNormalizeConfidences(const float parsac_min_prior_bin_confidence, std::vector<float>& confidences) {
  float confidenceSum = 0;
  const size_t N = confidences.size();
  for (size_t i = 0; i < N; ++i) {
    confidences[i] = std::max(parsac_min_prior_bin_confidence, confidences[i]);
    confidenceSum += confidences[i];
  }
  const float norm = 1.0 / confidenceSum;
  for (size_t i = 0; i < N; ++i) {
    confidences[i] *= norm;
  }
}

// cppcheck-suppress unusedFunction
void AccumulateConfidences(const std::vector<float>& confidences, std::vector<float>& confidences_accumulated) {
  const size_t N = confidences.size();
  confidences_accumulated.resize(N + 1);
  confidences_accumulated[0] = 0;
  for (size_t i = 0; i < N; ++i) {
    confidences_accumulated[i + 1] = confidences_accumulated[i] + confidences[i];
  }
  const float norm = 1.f / confidences_accumulated[N];
  for (size_t i = 0; i < N; ++i) {
    confidences_accumulated[i] *= norm;
  }
}

size_t GetBinIndex(const double coord, const double norm_offset, const double bin_size, const size_t number_of_bins) {
  assert(bin_size > 0 && "bin_size must be greater then 0.");
  const int32_t bin_index{static_cast<int32_t>(std::floor((coord + norm_offset) / bin_size))};

  return static_cast<size_t>(std::clamp(bin_index, static_cast<int32_t>(0), static_cast<int32_t>(number_of_bins) - 1));
}
}  // namespace rdvio