# rdvio utils
target_sources(${PROJECT_NAME}
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src/parsac_utils.cpp
)

target_include_directories(${PROJECT_NAME}
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
        $<INSTALL_INTERFACE:include>
)

if (BUILD_TESTING)
    find_package(GTest REQUIRED)

    set(TEST_LIST
            test_imu_parsac
            test_parsac
            test_ransac
            test_sampler
            test_parsac_utils
    )

    foreach (TEST_NAME ${TEST_LIST})
        set(EXEC_NAME ${TEST_NAME})

        add_executable(${EXEC_NAME})
        target_sources(${EXEC_NAME} PRIVATE
                "test/${EXEC_NAME}.cpp")
        target_link_libraries(${EXEC_NAME} PRIVATE
                ${PROJECT_NAME}
                ${GTEST_LIBRARIES}
                gtest_main
        )
        target_include_directories(${EXEC_NAME} PRIVATE
                ${GTEST_INCLUDE_DIRS}
        )
        add_test(
                NAME ${EXEC_NAME}
                COMMAND $<TARGET_FILE:${EXEC_NAME}>
                WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        )
    endforeach (TEST_NAME)
endif (BUILD_TESTING)