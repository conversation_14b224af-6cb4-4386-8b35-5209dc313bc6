#pragma once

#include <rdvio/types.h>

namespace rdvio {

float ComputeScore(const size_t num_valid_bins, const std::vector<size_t>& valid_bin_data_sizes,
                   const std::vector<vector<2>>& bin_locations, const std::vector<size_t>& map_valid_bin_to_bin,
                   const std::vector<std::vector<size_t>>& valid_bin_inliers, std::vector<float>& valid_bin_confidences,
                   const bool do_weighting, const double m_dynamic_probability = 0.0,
                   const std::vector<float>& m_validBinLens = {});

void ConvertInliersListToValidBin(const size_t num_valid_bins, const std::vector<size_t>& map_data_to_valid_bin,
                                  const std::vector<char>& inliers_mask,
                                  std::vector<std::vector<size_t>>& valid_bin_inliers);

void ConvertConfidencesBinToValidBin(const size_t num_valid_bins, const std::vector<size_t>& map_valid_bin_to_bin,
                                     const std::vector<float>& bin_confidences,
                                     std::vector<float>& valid_bin_confidences);

void ConvertConfidencesValidBinToBin(const size_t num_bins, const std::vector<size_t>& map_bin_to_valid_bin,
                                     const std::vector<float>& valid_bin_confidences,
                                     std::vector<float>& bin_confidences);

void ThresholdAndNormalizeConfidences(const float parsac_min_prior_bin_confidence, std::vector<float>& confidences);

void AccumulateConfidences(const std::vector<float>& confidences, std::vector<float>& confidences_accumulated);

template <class Data, class Sample>
void MakeSample(Data&& data, Sample&& sample, size_t idata, size_t isample) {
  size_t I = std::tuple_size<typename std::remove_reference<Data>::type>::value;
  std::get<0>(sample)[isample] = std::get<0>(data)[idata];
  std::get<1>(sample)[isample] = std::get<1>(data)[idata];
}

/**
 * @brief Convert a coordinate to a clamped bin index.
 *
 * @param coordinate      Raw coordinate (x or y)
 * @param norm_offset     Normalisation offset
 * @param bin_size        Width or height of a single bin.
 * @param number_of_bins  Total number of bins in this dimension.
 * @return                Index in the range [0, number_of_bins-1].
 */
size_t GetBinIndex(const double coord, const double norm_offset, const double bin_size, const size_t number_of_bins);

}  // namespace rdvio