#pragma once

#include <rdvio/types.h>
#include <rdvio/util/parsac_utils.h>
#include <rdvio/util/sampler.h>

namespace rdvio {

class ParsacBase {
 public:
  double threshold;
  double confidence;
  size_t max_iteration;
  int seed;

  size_t inlier_count;
  std::vector<char> inlier_mask;

  ParsacBase(const double threshold, const double confidence, const size_t max_iteration, const int seed)
      : threshold(threshold),
        confidence(confidence),
        max_iteration(max_iteration),
        seed(seed),
        m_parsacMinPriorBinConfidence(0.5) {}

 protected:
  template <size_t ModelDoF, typename... DataTypes>
  void GenerateSample(Sampler& sampler, LotBox& lotbox, std::tuple<const std::vector<DataTypes>&...>& tdata,
                      std::tuple<std::array<DataTypes, ModelDoF>...>& tsample) const {
    for (size_t si = 0; si < ModelDoF; ++si) {
      size_t sample_index;
      if (m_nValidBins > 20) {
        sample_index = sampler.DrawByWeight();
      } else {
        sample_index = lotbox.DrawWithoutReplacement();
      }
      MakeSample(tdata, tsample, sample_index, si);
    }
  }

  template <typename ModelType, typename ModelEvaluator, typename... DataTypes>
  std::tuple<size_t, std::vector<char>> CalculateInliers(const size_t size, const ModelType& current_model,
                                                         const std::vector<DataTypes>&... data) const {
    ModelEvaluator eval(current_model);

    size_t current_inlier_count{0};
    std::vector<char> current_inlier_mask(size, 0);
    for (size_t i = 0; i < size; ++i) {
      const double error{eval(data[i]...)};

      if (error <= threshold) {
        current_inlier_count++;
        current_inlier_mask[i] = 1;
      }
    }

    return {current_inlier_count, current_inlier_mask};
  }

  void UpdateState(const size_t _inlier_count, const size_t size, std::vector<char>& current_inlier_mask,
                   size_t& iter_max) {
    inlier_count = _inlier_count;
    inlier_mask.swap(current_inlier_mask);

    const double K = log(std::max(1 - confidence, 1.0e-5));
    const double inlier_ratio = inlier_count / (double)size;
    const double N = K / log(1 - pow(inlier_ratio, 5));

    if (N < (double)iter_max) {
      iter_max = (size_t)ceil(N);
    }
  }

  void SetBins(const int nBinsX, const int nBinsY) {
    m_nBinsX = nBinsX;
    m_nBinsY = nBinsY;
    m_nBins = m_nBinsX * m_nBinsY;
    m_BinHeight = 2 * m_norm_scale / m_nBinsY;
    m_BinWidth = 2 * m_norm_scale / m_nBinsX;
  }

  void CreateBucket() {
    m_binLocations.reserve(m_nBins);
    float y = m_BinHeight * 0.5;
    for (size_t i = 0; i < m_nBinsY; ++i, y += m_BinHeight) {
      float x = m_BinWidth * 0.5;
      for (size_t j = 0; j < m_nBinsX; ++j, x += m_BinWidth) {
        m_binLocations.emplace_back(vector<2>(x - m_norm_scale, y - m_norm_scale));
      }
    }
  }

  size_t m_nBinsX, m_nBinsY, m_nBins;
  std::vector<vector<2>> m_binLocations;
  std::vector<size_t> m_mapBinToValidBin;
  std::vector<size_t> m_mapValidBinToBin;
  std::vector<size_t> m_mapDataToValidBin;
  std::vector<std::vector<size_t>> m_validBinData;
  std::vector<size_t> m_validBinDataSizes;

  std::vector<float> m_validBinConfidences;
  std::vector<float> m_validBinConfidencesPrior;
  std::vector<float> m_validBinConfidencesAccumulatedPrior;
  float m_parsacMinPriorBinConfidence;

  size_t m_nValidBins;
  float m_BinHeight;
  float m_BinWidth;

  double m_norm_scale = 1.0;
};

}  // namespace rdvio
