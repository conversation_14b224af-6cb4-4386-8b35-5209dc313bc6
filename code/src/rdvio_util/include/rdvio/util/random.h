#pragma once

#include <rdvio/types.h>

namespace rdvio {

inline unsigned int GetRandomSeed() {
  static std::random_device rd;
  return rd();
}

class RandomBase {
 public:
  RandomBase() { this->Seed(); }

  void Seed() { engine_.seed(GetRandomSeed()); }

  void Seed(unsigned int value) { engine_.seed(value); }

 protected:
  std::default_random_engine engine_;
};

template <typename T>
class UniformInteger : public RandomBase {
 public:
  UniformInteger(T left = T(0), T right = std::numeric_limits<T>::max()) : distribution_(left, right) {}

  void Param(T left, T right) { distribution_.param(std::uniform_int_distribution<T>::param_type(left, right)); }

  T Next() { return distribution_(engine_); }

  T Next(T left, T right) {
    return distribution_(engine_, typename std::uniform_int_distribution<T>::param_type(left, right));
  }

 private:
  std::uniform_int_distribution<T> distribution_;
};

class LotBox {
 public:
  LotBox(size_t size) : cap_(0), lots_(size) { std::iota(lots_.begin(), lots_.end(), 0); }

  size_t DrawWithReplacement() {
    const size_t result{DrawWithoutReplacement()};
    RefillLast();
    return result;
  }

  size_t DrawWithoutReplacement() {
    if (Remaining() > 1) {
      std::swap(lots_[cap_], lots_[dice_.Next(cap_, lots_.size() - 1)]);
      const size_t result{lots_[cap_]};
      cap_++;
      return result;
    } else if (Remaining() == 1) {
      cap_++;
      return lots_.back();
    } else {
      return size_t(-1);  // Hey we have nothing left!
    }
  }

  void RefillLast(size_t n = 1) {
    if (cap_ > n) {
      cap_ -= n;
    } else {
      cap_ = 0;
    }
  }

  void RefillAll() { cap_ = 0; }

  size_t Remaining() const { return lots_.size() - cap_; }

  void Seed() { dice_.Seed(GetRandomSeed()); }

  void Seed(unsigned int value) { dice_.Seed(value); }

 private:
  size_t cap_;
  std::vector<size_t> lots_;
  UniformInteger<size_t> dice_;
};

}  // namespace rdvio