#pragma once

#include <rdvio/types.h>
#include <yaml-cpp/yaml.h>

namespace rdvio::utils {

struct Exception : public std::runtime_error {
  Exception(const std::string& what) : std::runtime_error(what) {}
};

struct LoadException : public Exception {
  LoadException(const std::string& filename) : Exception("cannot load config " + filename) {}
};

struct ParseException : public Exception {
  ParseException(const std::string& message) : Exception(message) {}
};

struct ConfigMissingException : public Exception {
  ConfigMissingException(const std::string& config_path) : Exception("config \"" + config_path + "\" is mandatory") {}
};

struct TypeErrorException : public Exception {
  TypeErrorException(const std::string& config_path) : Exception("config \"" + config_path + "\" has wrong type") {}
};

YAML::Node LoadFile(const std::string& path) {
  try {
    const auto node{YAML::LoadFile(path)};
    return node;
  } catch (const YAML::ParserException& parse_error) {
    throw ParseException(parse_error.what());
  } catch (...) {
    throw LoadException(path);
  }
}

YAML::Node FindNode(const YAML::Node& root, const std::string& path, const bool mandatory = false) {
  std::stringstream ss(path);
  std::string child;
  YAML::Node node = root;
  while (std::getline(ss, child, '.')) {
    node.reset(node[child]);
  }
  if (not node) {
    if (mandatory) {
      throw ConfigMissingException(path);
    }
  } else {
    node.SetTag(path);
  }
  return node;
}

void RequireVector(const YAML::Node& node, const size_t n) {
  if (node.size() != n) {
    throw TypeErrorException(node.Tag());
  }
  for (size_t i = 0; i < n; ++i) {
    if (not node[i].IsScalar()) {
      throw TypeErrorException(node.Tag());
    }
  }
}

template <typename T>
void AssignValue(const YAML::Node& node, T& value) {
  if (not node.IsScalar()) {
    throw TypeErrorException(node.Tag());
  }
  value = node.as<T>();
}

template <typename T>
void AssignValue(const YAML::Node& node, const std::string& name, const bool mandatory, T& value) {
  if (auto required_node = utils::FindNode(node, name, mandatory)) {
    utils::AssignValue(required_node, value);
  }
}

template <typename T>
void AssignVector(const YAML::Node& node, T& vec) {
  RequireVector(node, vec.size());
  for (size_t i = 0; i < vec.size(); ++i) {
    vec[i] = node[i].as<double>();
  }
}

template <typename T>
void AssignVector(const YAML::Node& node, const std::string& name, const bool mandatory, T& value) {
  if (auto required_node = utils::FindNode(node, name, mandatory)) {
    utils::AssignVector(required_node, value);
  }
}

template <typename T>
void AssignMatrix(const YAML::Node& node, T& mat) {
  RequireVector(node, mat.rows() * mat.cols());
  for (size_t i = 0; i < mat.rows(); ++i) {
    for (size_t j = 0; j < mat.cols(); ++j) {
      mat(i, j) = node[i * mat.cols() + j].template as<double>();
    }
  }
}

template <typename T>
void AssignMatrix(const YAML::Node& node, const std::string& name, const bool mandatory, T& value) {
  if (auto required_node = utils::FindNode(node, name, mandatory)) {
    utils::AssignMatrix(required_node, value);
  }
}

}  // namespace rdvio::utils