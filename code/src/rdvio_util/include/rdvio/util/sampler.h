#pragma once

#include <rdvio/util/random.h>

namespace rdvio {

struct Sampler {
  Sampler(std::vector<float>& confidences_accumulated) : confidences_accumulated_(confidences_accumulated) {
    constexpr unsigned int seed{0};
    srand(seed);
  };

  bool IsSampled(const size_t idx) {
    if (std::size(sampled_bin_index_) == 0) {
      return false;
    }

    for (int i{0}; i < std::size(sampled_bin_index_); ++i) {
      if (idx == sampled_bin_index_[i]) {
        return true;
      }
    }
    return false;
  }

  size_t DrawByWeight() {
    size_t index;
    do {
      const float r{rand() / static_cast<float>(RAND_MAX)};
      index = static_cast<size_t>(
          std::upper_bound(std::cbegin(confidences_accumulated_) + 1, std::cend(confidences_accumulated_), r) -
          std::cbegin(confidences_accumulated_) - 1);
    } while (IsSampled(index));
    sampled_bin_index_.push_back(index);

    return index;
  }

  void RefillAll() { sampled_bin_index_.clear(); }

 private:
  // WARN(Jack): Is there a good reason that this is a mutable reference? If this is not needed this might cause
  // unexpected behavior for future users because the state can change implicitly :(
  std::vector<float>& confidences_accumulated_;
  std::vector<size_t> sampled_bin_index_;
};

}  // namespace rdvio