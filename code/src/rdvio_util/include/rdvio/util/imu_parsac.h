#pragma once

#include <rdvio/types.h>
#include <rdvio/util/parsac.h>
#include <rdvio/util/parsac_base.h>
#include <rdvio/util/parsac_utils.h>
#include <rdvio/util/random.h>

namespace rdvio {

// TODO(Jack): The authors tried there best to make this code generic, but failed. The typename ModelType is redundant
// because there are several hardcoded matrix<4>::Identity() return statements as part of some conditionals. This means
// that we actually do not have the choice of ModelType. Remove this concept and replace it directly with matrix<4>.
template <size_t ModelDoF, typename ModelType, typename ModelSolver, typename ModelEvaluator>
struct IMU_Parsac : public ParsacBase {
  ModelType model;

  matrix<4> prior_model;
  std::vector<size_t> m_lens;
  std::vector<char> m_prior_inliers_mask;

  IMU_Parsac(double threshold, double confidence = 0.999, size_t max_iteration = 1000, int seed = 0)
      : ParsacBase(threshold, confidence, max_iteration, seed) {}

  template <typename... DataTypes>
  ModelType solve(std::vector<float>& binConfidences, const std::vector<DataTypes>&... data) {
    std::tuple<const std::vector<DataTypes>&...> tdata = std::make_tuple(std::cref(data)...);
    size_t size = std::get<0>(tdata).size();
    auto& pts1 = std::get<0>(tdata);
    auto& pts2 = std::get<1>(tdata);

    LotBox lotbox(size);
    lotbox.Seed(seed);

    inlier_count = 0;

    if (size < ModelDoF) {
      std::vector<char> _(size, 0);
      inlier_mask.swap(_);
      return model;
    }

    SetBins(20, 20);
    CreateBucket();
    BucketData(pts2);
    ConvertConfidencesBinToValidBin(m_nValidBins, m_mapValidBinToBin, binConfidences, m_validBinConfidencesPrior);
    ThresholdAndNormalizeConfidences(m_parsacMinPriorBinConfidence, m_validBinConfidencesPrior);
    AccumulateConfidences(m_validBinConfidencesPrior, m_validBinConfidencesAccumulatedPrior);

    Sampler sampler(m_validBinConfidencesAccumulatedPrior);

    if (!ComputePriorDistribution(pts1, pts2)) {
      inlier_mask = std::vector<char>(size, 1);
      return matrix<4>::Identity();
    }

    size_t iter_max = max_iteration;
    float scoreMax = -FLT_MAX;
    const double K = log(std::max(1 - confidence, 1.0e-5));

    std::vector<std::vector<size_t>> validBinInliersBest;
    for (size_t iter = 0; iter < iter_max; ++iter) {
      lotbox.RefillAll();
      sampler.RefillAll();
      std::tuple<std::array<DataTypes, ModelDoF>...> tsample;
      GenerateSample(sampler, lotbox, tdata, tsample);

      std::vector<ModelType> models{apply(ModelSolver(), tsample)};
      for (const auto& current_model : models) {
        auto [current_inlier_count, current_inlier_mask] =
            CalculateInliers<ModelType, ModelEvaluator, DataTypes...>(size, current_model, data...);

        size_t overlap_inlier_count = 0;
        std::vector<char> overlap_inlier_mask(size, 0);
        for (int i = 0; i < size; i++) {
          if (m_prior_inliers_mask[i] && current_inlier_mask[i]) {
            overlap_inlier_mask[i] = 1;
            overlap_inlier_count++;
          }
        }

        if (overlap_inlier_count < ModelDoF) {
          continue;
        }

        std::vector<std::vector<size_t>> validBinInliers;
        ConvertInliersListToValidBin(m_nValidBins, m_mapDataToValidBin, current_inlier_mask, validBinInliers);

        const float score =
            ComputeScore(m_nValidBins, m_validBinDataSizes, m_binLocations, m_mapValidBinToBin, validBinInliers,
                         m_validBinConfidences, true, m_dynamic_probability, m_validBinLens);

        if (score > scoreMax || score == scoreMax && (overlap_inlier_count > inlier_count)) {
          scoreMax = score;
          validBinInliersBest = validBinInliers;
          model = current_model;  // Cannot easily be made part of base class due to templating problems

          UpdateState(overlap_inlier_count, size, current_inlier_mask, iter_max);
        }
      }
    }

    if (inlier_count < ModelDoF) {
      inlier_mask = std::vector<char>(size, 1);
      return matrix<4>::Identity();
    }

    ComputeScore(m_nValidBins, m_validBinDataSizes, m_binLocations, m_mapValidBinToBin, validBinInliersBest,
                 m_validBinConfidences, true, m_dynamic_probability, m_validBinLens);

    ConvertConfidencesValidBinToBin(m_nBins, m_mapBinToValidBin, m_validBinConfidences, binConfidences);

    return model;
  }

  void SetLens(const std::vector<size_t>& lens) { m_lens = lens; }

  void SetPriorPose(matrix<3> R, vector<3> t) {
    prior_model = matrix<4>::Identity();
    prior_model.block<3, 3>(0, 0) = R;
    prior_model.block<3, 1>(0, 3) = t;
  }

  void SetNormScale(double scale = 1.0) { m_norm_scale = scale; }

  template <typename... DataTypes>
  bool ComputePriorDistribution(const std::vector<DataTypes>&... data) {
    std::tuple<const std::vector<DataTypes>&...> tdata = std::make_tuple(std::cref(data)...);
    size_t size = std::get<0>(tdata).size();

    auto& pts1 = std::get<0>(tdata);
    auto& pts2 = std::get<1>(tdata);

    size_t inlier_count = 0;
    m_prior_inliers_mask = std::vector<char>(size, 0);

    ModelEvaluator eval(prior_model);
    for (size_t i = 0; i < size; ++i) {
      double error = eval(data[i]...);
      if (error <= threshold * 2.0) {
        inlier_count++;
        m_prior_inliers_mask[i] = 1;
      }
    }

    // if(inlier_count < 20 ) //|| inlier_count * 1.0 / size < 0.2
    if ((double)inlier_count / size < 0.15 || inlier_count < 20) {  //|| inlier_count * 1.0 / size < 0.2
      return false;
    }

    return true;
  }

  void SetDynamicProbability(const double& p) { m_dynamic_probability = p; }

 private:
  std::vector<float> m_validBinLens;
  double m_dynamic_probability = 0;

  template <typename DataTypes>
  void BucketData(const std::vector<DataTypes>& pts) {
    const size_t N = pts.size();
    m_mapDataToValidBin.resize(N);
    m_mapBinToValidBin = std::vector<size_t>(m_nBins, SIZE_MAX);
    for (size_t i = 0; i < N; ++i) {
      const vector<2>& p1 = pts[i];

      // Duplicate Code here and parsac.h
      const size_t binX{GetBinIndex(p1[0], m_norm_scale, m_BinWidth, m_nBinsX)};
      const size_t binY{GetBinIndex(p1[1], m_norm_scale, m_BinHeight, m_nBinsY)};
      const size_t iBin{binX + m_nBinsX * binY};

      const size_t iBinValid = m_mapBinToValidBin[iBin];
      if (iBinValid == SIZE_MAX) {
        m_mapBinToValidBin[iBin] = size_t(m_mapValidBinToBin.size());
        m_mapDataToValidBin[i] = size_t(m_mapValidBinToBin.size());

        m_mapValidBinToBin.push_back(iBin);
        m_validBinData.push_back(std::vector<size_t>(1, i));
        m_validBinDataSizes.push_back(1);
        m_validBinLens.push_back(m_lens[i]);
      } else {
        m_mapDataToValidBin[i] = iBinValid;
        m_validBinData[iBinValid].push_back(i);
        ++m_validBinDataSizes[iBinValid];
        m_validBinLens[iBinValid] += m_lens[i];
      }
    }

    m_nValidBins = m_validBinDataSizes.size();

    for (size_t i = 0; i < m_nValidBins; i++) {
      m_validBinLens[i] /= m_validBinDataSizes[i];
    }
  }
};

}  // namespace rdvio
