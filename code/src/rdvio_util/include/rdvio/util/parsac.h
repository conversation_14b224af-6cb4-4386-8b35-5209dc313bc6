#pragma once

#include <spl-logging/logging.hpp>

#include "rdvio/types.h"
#include "rdvio/util.hpp"
#include "rdvio/util/parsac_base.h"
#include "rdvio/util/parsac_utils.h"
#include "rdvio/util/sampler.h"

namespace rdvio {

template <size_t ModelDoF, typename ModelType, typename ModelSolver, typename ModelEvaluator>
class Parsac : public ParsacBase {
 public:
  ModelType model;

  Parsac(double threshold, double confidence = 0.999, size_t max_iteration = 1000, int seed = 0)
      : ParsacBase(threshold, confidence, max_iteration, seed) {}

  template <typename... DataTypes>
  ModelType solve(std::vector<float>& binConfidences, const std::vector<DataTypes>&... data) {
    constexpr auto logger_scope{"Parsac::solve"};
    std::tuple<const std::vector<DataTypes>&...> tdata = std::make_tuple(std::cref(data)...);
    size_t size = std::get<0>(tdata).size();
    auto& pts1 = std::get<0>(tdata);
    auto& pts2 = std::get<1>(tdata);

    LotBox lotbox(size);
    lotbox.Seed(seed);

    inlier_count = 0;

    if (size < ModelDoF) {
      utils::LogStatistics(logger_scope, "The #points {} is less than the #DoF {}", size, ModelDoF);
      // TODO(Jack): why do we do this swap here, instead of just returning it directly?
      std::vector<char> _(size, 0);
      inlier_mask.swap(_);
      return model;
    }

    SetBins(20, 20);
    CreateBucket();
    BucketData(pts2);  // Segfaults here with large values!
    ConvertConfidencesBinToValidBin(m_nValidBins, m_mapValidBinToBin, binConfidences, m_validBinConfidencesPrior);
    ThresholdAndNormalizeConfidences(m_parsacMinPriorBinConfidence, m_validBinConfidencesPrior);
    AccumulateConfidences(m_validBinConfidencesPrior, m_validBinConfidencesAccumulatedPrior);

    Sampler sampler(m_validBinConfidencesAccumulatedPrior);

    size_t iter_max = max_iteration;
    float scoreMax = 0;

    std::vector<std::vector<size_t>> validBinInliersBest;
    for (size_t iter = 0; iter < iter_max; ++iter) {
      lotbox.RefillAll();
      sampler.RefillAll();

      std::tuple<std::array<DataTypes, ModelDoF>...> tsample;
      GenerateSample(sampler, lotbox, tdata, tsample);

      std::vector<ModelType> models{apply(ModelSolver(), tsample)};
      for (const auto& current_model : models) {
        auto [current_inlier_count, current_inlier_mask] =
            CalculateInliers<ModelType, ModelEvaluator, DataTypes...>(size, current_model, data...);

        std::vector<std::vector<size_t>> validBinInliers;
        ConvertInliersListToValidBin(m_nValidBins, m_mapDataToValidBin, current_inlier_mask, validBinInliers);

        const float score{ComputeScore(m_nValidBins, m_validBinDataSizes, m_binLocations, m_mapValidBinToBin,
                                       validBinInliers, m_validBinConfidences, false)};

        if (score > scoreMax || score == scoreMax && (current_inlier_count > inlier_count)) {
          scoreMax = score;
          validBinInliersBest = validBinInliers;
          model = current_model;  // Cannot easily be made part of base class due to templating problems

          UpdateState(current_inlier_count, size, current_inlier_mask, iter_max);
        }
      }
    }

    const auto score{ComputeScore(m_nValidBins, m_validBinDataSizes, m_binLocations, m_mapValidBinToBin,
                                  validBinInliersBest, m_validBinConfidences, false)};

    ConvertConfidencesValidBinToBin(m_nBins, m_mapBinToValidBin, m_validBinConfidences, binConfidences);

    utils::LogStatistics(logger_scope, "Is Converged? {}", iter_max < max_iteration);
    utils::LogStatistics(logger_scope, "#Score {}", score);
    utils::LogStatistics(logger_scope, "#Iterations {}", iter_max);
    utils::LogStatistics(logger_scope, "#Inliers count {}", inlier_count);

    return model;
  }

 private:
  // TODO(Jack): This is another place where the authors tried to make the code generic but somehow failed. For example
  // this method reguires that DataTypes is a vector of at least length two! When it calculated iBin it indexes into
  // this, and if it is not a vector of at least length two you get a segfault! This is bad design. Instead simply
  // hardcode the type as vector<2> because (I think) that should work with all versions of the parsac usage.
  template <typename DataTypes>
  void BucketData(const std::vector<DataTypes>& pts) {
    const size_t N = pts.size();
    m_mapDataToValidBin.resize(N);
    m_mapBinToValidBin = std::vector<size_t>(m_nBins, SIZE_MAX);
    for (size_t i = 0; i < N; ++i) {
      const vector<2>& p1 = pts[i];

      const size_t binX{GetBinIndex(p1[0], m_norm_scale, m_BinWidth, m_nBinsX)};
      const size_t binY{GetBinIndex(p1[1], m_norm_scale, m_BinHeight, m_nBinsY)};
      const size_t iBin{binX + m_nBinsX * binY};

      const size_t iBinValid = m_mapBinToValidBin[iBin];
      if (iBinValid == SIZE_MAX) {
        m_mapBinToValidBin[iBin] = size_t(m_mapValidBinToBin.size());
        m_mapDataToValidBin[i] = size_t(m_mapValidBinToBin.size());

        m_mapValidBinToBin.push_back(iBin);
        m_validBinData.push_back(std::vector<size_t>(1, i));
        m_validBinDataSizes.push_back(1);
      } else {
        m_mapDataToValidBin[i] = iBinValid;
        m_validBinData[iBinValid].push_back(i);
        ++m_validBinDataSizes[iBinValid];
      }
    }
    m_nValidBins = m_validBinDataSizes.size();
  }
};

}  // namespace rdvio
