#include <fstream>
#include <string>

#include <Eigen/Dense>

#include "rdvio/types.h"

namespace rdvio {

enum class TrajectoryType { TUM, ODOMETRY };

class TrajectoryLogger {
 public:
  struct TrajectoryPoint {
    // TODO(Vipul): Use chrono times instead.
    double timestamp;  // in seconds
    Eigen::Matrix4d pose;
    std::optional<const Eigen::Vector3d> linear_velocity;
    std::optional<const Eigen::Vector3d> angular_velocity;
  };

  TrajectoryLogger(const std::string& filename, const TrajectoryType trajectory_type = TrajectoryType::TUM,
                   const std::string& delimeter = " ")
      : trajectory_type_{trajectory_type}, delimeter_{delimeter} {
    try {
      // First, clear the file by opening in trunc mode
      std::ofstream clear_file(filename, std::ios::out | std::ios::trunc);
      clear_file.close();

      // Then open it in append mode for logging
      file_.open(filename, std::ios::out | std::ios::app);
      if (not file_.is_open()) {
        std::cerr << "Failed to open file: " << filename << std::endl;
      }

      std::vector<std::string> headers;
      if (trajectory_type_ == TrajectoryType::TUM) {
        delimeter_ = " ";  // Only space delimeter is supported for TUM dataset format.
        headers = {"#", "timestamp(s)", "tx", "ty", "tz", "qx", "qy", "qz", "qw"};
      } else if (trajectory_type_ == TrajectoryType::ODOMETRY) {
        headers = {"timestamp(s)", "tx", "ty", "tz", "qx", "qy", "qz", "qw", "vx", "vy", "vz", "wx", "wy", "wz"};
      } else {
        throw std::runtime_error("Linear velocity and angular");
      }
      WriteHeaders(headers, delimeter_);

    } catch (const std::exception& e) {
      std::cout << "Error initializing the logger: " << e.what() << std::endl;
    }
  }

  ~TrajectoryLogger() { Destroy(); }

  bool Log(const Eigen::Matrix4d& pose, const double timestamp,
           std::optional<const Eigen::Vector3d> linear_velocity = std::nullopt,
           std::optional<const Eigen::Vector3d> angular_velocity = std::nullopt) {
    if (trajectory_type_ == TrajectoryType::ODOMETRY) {
      if (not linear_velocity.has_value() or not angular_velocity.has_value()) {
        throw std::runtime_error("Linear velocity and angular velocity must be provided when using Odometry logger");
      }
    }

    const TrajectoryPoint point{timestamp, pose, linear_velocity, angular_velocity};
    const auto status{WriteTrajectoryPoint(point)};

    return status;
  }

  void Destroy() {
    if (file_.is_open()) {
      file_.close();
    }
  }

 private:
  bool WriteTrajectoryPoint(const TrajectoryPoint& point) {
    try {
      if (not file_.is_open()) {
        std::cout << "The log file is not oppened" << std::endl;
        return false;
      }

      // Extract translation vector (Tx, Ty, Tz)
      const Eigen::Vector3d translation{point.pose.block<3, 1>(0, 3)};
      // Extract rotation matrix (R)
      const Eigen::Matrix3d rotation{point.pose.block<3, 3>(0, 0)};
      // Convert rotation matrix to quaternion
      const Eigen::Quaterniond quaternion(rotation);

      if (trajectory_type_ == TrajectoryType::TUM) {
        LogValue(point.timestamp, delimeter_);
        LogVector(translation, delimeter_, delimeter_);
        LogVector(quaternion.coeffs(), delimeter_, "\n");
      } else if (trajectory_type_ == TrajectoryType::ODOMETRY) {
        LogValue(point.timestamp, delimeter_);
        LogVector(translation, delimeter_, delimeter_);
        LogVector(quaternion.coeffs(), delimeter_, delimeter_);
        LogVector(point.linear_velocity.value(), delimeter_, delimeter_);
        LogVector(point.angular_velocity.value(), delimeter_, "\n");
      }

      return true;
    } catch (const std::exception& e) {
      std::cout << "Error writing pose to file: " << e.what() << std::endl;
      return false;
    }
  }

  void LogValue(const double value, const std::string& end_delimeter) {
    file_ << std::fixed << std::setprecision(15) << value << end_delimeter;
  }

  void LogVector(const Eigen::VectorXd& vector, const std::string& mid_delimeter, const std::string& end_delimeter) {
    size_t i = 0;
    for (; i < vector.size() - 1; i++) {
      file_ << std::fixed << std::setprecision(15) << vector(i) << mid_delimeter;
    }
    file_ << std::fixed << std::setprecision(15) << vector(i) << end_delimeter;
  }

  void WriteHeaders(const std::vector<std::string>& headers, const std::string& delimeter) {
    size_t i = 0;
    for (; i < headers.size() - 1; i++) {
      file_ << headers[i] << delimeter;
    }
    file_ << headers[i] << std::endl;
  }

  std::ofstream file_;
  TrajectoryType trajectory_type_;
  std::string delimeter_;
};

}  // namespace rdvio