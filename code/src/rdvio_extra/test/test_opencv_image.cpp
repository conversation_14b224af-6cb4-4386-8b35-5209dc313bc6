#include <gtest/gtest.h>

#include "rdvio/extra/camera.h"
#include "rdvio/extra/opencv_image.h"
#include "rdvio/rdvio.hpp"
#include "rdvio/testing_util/unit/image_frontend_fixture.hpp"

using namespace rdvio;

TEST_F(ImageFrontendFixture, DetectKeypoints) {
  const auto feature_tracker_config = config_->GetFeatureTrackerConfig();
  std::vector<vector<2>> keypoints;
  opencv_image1_->DetectKeypoints(feature_tracker_config_.max_keypoint_detection,
                                  feature_tracker_config_.min_keypoint_distance, keypoints);

  ASSERT_EQ(keypoints.size(), 165);
  for (size_t i = 0; i < 165; i++) {
    // Detected keypoints shouldn't be zeros.
    ASSERT_FALSE(keypoints[i].isApprox(vector<2>{0, 0}));
  }
};

TEST_F(ImageFrontendFixture, TrackKeypoints) {
  std::vector<vector<2>> curr_keypoints;
  opencv_image1_->DetectKeypoints(feature_tracker_config_.max_keypoint_detection,
                                  feature_tracker_config_.min_keypoint_distance, curr_keypoints);

  std::vector<vector<2>> next_keypoints;
  std::vector<char> status;
  opencv_image1_->TrackKeypoints(opencv_image2_.get(), curr_keypoints, next_keypoints, status);

  ASSERT_EQ(curr_keypoints.size(), 165);
  ASSERT_EQ(next_keypoints.size(), 165);

  int valid_tracks_count{0};
  for (size_t i = 0; i < 165; i++) {
    if (status[i] == 1) {
      // The tracked keypoint shouldn't be identical to the current keypoint.
      ASSERT_FALSE(curr_keypoints[i].isApprox(next_keypoints[i]));
      // The tracked keypoint shouldn't be very far from the current keypoint.
      ASSERT_TRUE(curr_keypoints[i].isApprox(next_keypoints[i], 10));
      valid_tracks_count++;
    }
  }
  ASSERT_EQ(valid_tracks_count, 162);
};
