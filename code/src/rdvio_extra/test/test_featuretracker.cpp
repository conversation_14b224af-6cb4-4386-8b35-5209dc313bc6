#include <gtest/gtest.h>

#include "rdvio/extra/camera.h"
#include "rdvio/extra/opencv_image.h"
#include "rdvio/extra/poisson_disk_filter.h"
#include "rdvio/map/frame.h"
#include "rdvio/map/map.h"
#include "rdvio/rdvio.hpp"
#include "rdvio/testing_util/unit/image_frontend_fixture.hpp"

using namespace rdvio;

size_t GetNumTracks(std::unique_ptr<Frame>& curr_frame) {
  size_t count = 0;
  for (size_t i = 0; i < curr_frame->keypoint_num(); ++i) {
    if (const Track* const track = curr_frame->get_track(i); track) {
      count++;
    }
  }
  return count;
}

class FeatureTrackTest : public ImageFrontendFixture {
 protected:
  // cppcheck-suppress [unusedFunction, unmatchedSuppression]
  FeatureTrackTest() {
    frame1_ = ConstructFrame(config_, opencv_image1_);
    frame2_ = ConstructFrame(config_, opencv_image2_);
    // (Vipul): The test feature_track_without_motion uses the same frame to make sure that we don't get any motion from
    // static scenes. So, to generate a duplicate of frame1_, either we use the .clone() function of Frame or generate a
    // new ony by using the opencv_image1_. Currently, the frame .clone() is not tested yet here, I have generated a new
    // one.
    frame3_ = ConstructFrame(config_, opencv_image1_);
  }

  std::unique_ptr<Frame> ConstructFrame(std::shared_ptr<Config> config, std::shared_ptr<extra::OpenCvImage> image) {
    const auto imu_config{config->GetImuConfig()};

    auto frame = std::make_unique<Frame>();
    const auto camera_config{config->GetCameraConfig()};

    frame->K = camera_config.K;
    frame->image = image;
    frame->sqrt_inv_cov = frame->K.block<2, 2>(0, 0);
    frame->sqrt_inv_cov(0, 0) /= ::sqrt(camera_config.keypoint_noise_cov(0, 0));
    frame->sqrt_inv_cov(1, 1) /= ::sqrt(camera_config.keypoint_noise_cov(1, 1));
    frame->camera.Q_body_sensor = camera_config.camera_to_body_rotation;
    frame->camera.P_body_sensor = camera_config.camera_to_body_translation;
    frame->imu.Q_body_sensor = imu_config.imu_to_body_rotation;
    frame->imu.P_body_sensor = imu_config.imu_to_body_translation;
    frame->preintegration.cov_a = imu_config.accelerometer_noise_cov;
    frame->preintegration.cov_w = imu_config.gyroscope_noise_cov;
    frame->preintegration.cov_ba = imu_config.accelerometer_bias_noise_cov;
    frame->preintegration.cov_bg = imu_config.gyroscope_bias_noise_cov;
    frame->preintegration.delta.q = quaternion{1, 0, 0, 0};

    frame->detect_keypoints(feature_tracker_config_);

    return frame;
  }

  std::unique_ptr<Frame> frame1_;
  std::unique_ptr<Frame> frame2_;
  std::unique_ptr<Frame> frame3_;
};

TEST_F(FeatureTrackTest, feature_track_with_motion) {
  ASSERT_EQ(frame1_->keypoint_num(), 165);

  std::unique_ptr<Map> map = std::make_unique<Map>();
  map->attach_frame(std::move(frame1_));
  Frame* last_frame = map->get_frame(0);

  last_frame->track_keypoints(frame2_.get(), feature_tracker_config_);

  EXPECT_FALSE(frame2_->tag(FT_NO_TRANSLATION));  // We have motion in that frame
  ASSERT_EQ(GetNumTracks(frame2_), 162);
};

TEST_F(FeatureTrackTest, feature_track_without_motion) {
  std::unique_ptr<Map> map = std::make_unique<Map>();
  map->attach_frame(std::move(frame1_));
  Frame* last_frame = map->get_frame(0);

  last_frame->track_keypoints(frame3_.get(), feature_tracker_config_);

  ASSERT_TRUE(frame3_->tag(FT_NO_TRANSLATION));  // We have no motion in that frame
  ASSERT_EQ(GetNumTracks(frame3_), 165);
};
