#pragma once

#include <opencv2/opencv.hpp>
#include <spl-camera-models/universal_distortion_handler.hpp>

#include "rdvio/config.hpp"
#include "rdvio/types.h"

namespace rdvio::extra {

class Camera {
 public:
  Camera(const CameraConfig& config);
  std::vector<vector<2>> RectifyKeyPoints(const std::vector<vector<2>>& keypoints) const;
  std::vector<vector<2>> UnrectifyKeyPoints(const std::vector<vector<2>>& keypoints) const;
  cv::Mat RectifyImage(const cv::Mat& image) const;

  size_t GetTrackingMargin() const;
  bool DoImageRectification() const;

 private:
  std::vector<Eigen::Vector2f> ConvertToFloat(const std::vector<vector<2>>& input) const;
  std::vector<vector<2>> ConvertToDouble(const std::vector<Eigen::Vector2f>& input) const;

  const CameraConfig config_;
  std::shared_ptr<spl::camera_models::UniversalDistortionHandler> undistortion_handler_;

  matrix<3> K_;
  bool do_image_rectification_;
  size_t camera_tracking_margin_;
};

}  // namespace rdvio::extra
