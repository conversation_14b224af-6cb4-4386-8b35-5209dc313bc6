#pragma once

#include <ceres/cubic_interpolation.h>
#include <opencv2/opencv.hpp>

#include "rdvio/config.hpp"
#include "rdvio/extra/camera.h"
#include "rdvio/extra/image_frontend.hpp"
#include "rdvio/types.h"

namespace rdvio::extra {

std::vector<cv::Point2f> ToOpencv(const std::vector<vector<2>>& v);

std::vector<vector<2>> FromOpencv(const std::vector<cv::Point2f>& v);

class OpenCvImage : public ImageFrontend {
 public:
  OpenCvImage(const cv::Mat& image, const double timestamp, const std::shared_ptr<extra::Camera> camera,
              const OpticalFlowConfig& optical_flow_config, bool enable_timing = false);

  void DetectKeypointsImpl(const size_t max_points, const double keypoint_distance,
                           std::vector<vector<2>>& keypoints) const override;

  void TrackKeypointsImpl(const ImageFrontend* const next_image, const std::vector<vector<2>>& curr_keypoints,
                          std::vector<vector<2>>& next_keypoints, std::vector<char>& result_status) const override;

  void PreProcessImpl(const ClaheConfig& config) override;

  void ReleaseImageBufferImpl() override;

 private:
  std::vector<cv::Mat> image_pyramid;
  const std::shared_ptr<extra::Camera> camera_;

  static cv::CLAHE* Clahe(const double clip_limit, const int width, const int height);
  static cv::GFTTDetector* GoodFeaturesToTrack(const size_t max_points);

  const OpticalFlowConfig optical_flow_config_;

  static constexpr double gftt_quality_level{1.0e-3};
  static constexpr size_t gftt_min_distance{20};
  static constexpr size_t gftt_block_size{3};
  static constexpr bool gftt_use_harris_detector{true};
};

}  // namespace rdvio::extra
