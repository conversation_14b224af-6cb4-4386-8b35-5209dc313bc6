#pragma once

#include <algorithm>
#include <limits>
#include <string>
#include <vector>

#include <Eigen/Eigen>
#include <opencv2/opencv.hpp>

// Include rdvio types
#include "rdvio/map/frame.h"
#include "rdvio/map/track.h"
#include "rdvio/types.h"

namespace rdvio::extra {

/**
 * @brief Visualizes optical flow tracking results by drawing keypoints and saving the output image.
 *
 * @param cur_image_gray The current frame as a grayscale image
 * @param next_image_gray The next frame as a grayscale image
 * @param next_timestamp Timestamp of the next frame, used for filename generation
 * @param undistorted_curr_cv_points Vector of keypoint positions in the current frame (undistorted coordinates)
 * @param undistorted_next_cv_points Vector of tracked keypoint positions in the next frame (undistorted coordinates)
 * @param predicted_cv_points Vector of predicted keypoint positions based on motion model (IMU)
 * @param result_status Vector indicating tracking success for each keypoint (1 = success, 0 = failure)
 * @param output_path Directory path where the visualization image will be saved
 */
inline void DrawOpticalFlow(const cv::Mat& cur_image_gray, const cv::Mat& next_image_gray, const double next_timestamp,
                            const std::vector<cv::Point2f>& undistorted_curr_cv_points,
                            const std::vector<cv::Point2f>& undistorted_next_cv_points,
                            const std::vector<cv::Point2f>& predicted_cv_points, const std::vector<char>& result_status,
                            const std::string& output_path) {
  cv::Mat curr_img_color{};
  cv::Mat next_img_color{};
  cv::cvtColor(cur_image_gray, curr_img_color, cv::COLOR_GRAY2BGR);
  cv::cvtColor(next_image_gray, next_img_color, cv::COLOR_GRAY2BGR);

  // Count tracked keypoints and draw on current frame (blue), next frame (green), and predicted motion (red)
  size_t tracked_keypoints_count{0};
  const cv::Scalar blue_color{255, 0, 0};
  const cv::Scalar green_color{0, 255, 0};
  const cv::Scalar red_color{0, 0, 255};
  const cv::Scalar orange_color{0, 165, 255};
  constexpr size_t circle_radius{3};
  constexpr int line_thickness{-1};
  const size_t keypoint_count{result_status.size()};
  for (size_t i{0}; i < keypoint_count; ++i) {
    if (result_status[i]) {
      tracked_keypoints_count++;
      cv::circle(curr_img_color, undistorted_curr_cv_points[i], circle_radius, blue_color, line_thickness);
      cv::circle(next_img_color, undistorted_next_cv_points[i], circle_radius, green_color, line_thickness);
      cv::circle(next_img_color, predicted_cv_points[i], circle_radius, red_color, line_thickness);
    }
  }

  // Add keypoint count text to images
  const std::string count_text{"Keypoints: " + std::to_string(tracked_keypoints_count)};
  const cv::Point text_position{10, 30};
  constexpr double font_scale{0.7};
  constexpr int font_thickness{2};
  cv::putText(curr_img_color, count_text, text_position, cv::FONT_HERSHEY_SIMPLEX, font_scale, orange_color,
              font_thickness);
  cv::putText(next_img_color, count_text, text_position, cv::FONT_HERSHEY_SIMPLEX, font_scale, orange_color,
              font_thickness);

  // Create side-by-side visualization
  cv::Mat visualization{};
  cv::hconcat(curr_img_color, next_img_color, visualization);

  // Display the visualization
  const std::string next_timestamp_str{std::to_string(next_timestamp)};
  const std::string filename{output_path + "/optical_flow_tracking_" + next_timestamp_str + ".png"};
  cv::imwrite(filename, visualization);
}

/**
 * @brief Visualizes 3D landmarks projected onto the current frame with depth-based color coding.
 *
 * @param frame The current frame containing keypoints and their associated tracks
 * @param output_path Directory path where the visualization image will be saved
 */
inline void VisualizeDepthPoints(const rdvio::Frame* const frame, const std::string& output_path) {
  if (!frame || !frame->image || frame->image->GetImage().empty()) {
    return;
  }

  const rdvio::PoseState& camera_pose{frame->get_pose(frame->camera)};
  const rdvio::matrix<3>& camera_matrix{frame->K};

  // Collect and project valid 3D landmarks in a single pass
  struct ProjectedPoint {
    rdvio::vector<2> point_2d{};
    double depth{0.0};
    bool is_valid{false};
    int radius{3};
  };

  std::vector<ProjectedPoint> projected_points{};
  projected_points.reserve(frame->keypoint_num());

  double min_depth{std::numeric_limits<double>::infinity()};
  double max_depth{0.0};

  for (size_t keypoint_idx{0}; keypoint_idx < frame->keypoint_num(); ++keypoint_idx) {
    if (rdvio::Track* const track{frame->get_track(keypoint_idx)}; track) {
      if (track->all_tagged(rdvio::TT_VALID, rdvio::TT_TRIANGULATED, rdvio::TT_STATIC)) {
        const rdvio::vector<3> point_camera{camera_pose.q.conjugate() * (track->GetLandmarkPoint() - camera_pose.p)};
        const rdvio::vector<3> point_image{camera_matrix * point_camera};
        const rdvio::vector<2> point_2d{point_image.hnormalized()};
        const double depth{point_camera.norm()};
        const bool is_valid{depth > 0.0};

        if (is_valid) {
          min_depth = std::min(min_depth, depth);
          max_depth = std::max(max_depth, depth);
        }

        ProjectedPoint proj_point{};
        proj_point.point_2d = point_2d;
        proj_point.depth = depth;
        proj_point.is_valid = is_valid;
        proj_point.radius = std::max(3, static_cast<int>(3 + track->m_life * 0.1));

        projected_points.push_back(proj_point);
      }
    }
  }

  if (projected_points.empty()) {
    return;
  }

  const double depth_range{std::max(max_depth - min_depth, 1e-6)};
  cv::Mat visualization_image{};
  cv::cvtColor(frame->image->GetImage(), visualization_image, cv::COLOR_GRAY2BGR);

  for (const auto& proj_point : projected_points) {
    const cv::Point2f point{static_cast<float>(proj_point.point_2d.x()), static_cast<float>(proj_point.point_2d.y())};

    if (proj_point.is_valid) {
      const double normalized_depth{(proj_point.depth - min_depth) / depth_range};
      const double clamped_depth{std::clamp(normalized_depth, 0.0, 1.0)};

      // Create color gradient from red (close) to blue (far)
      const cv::Scalar color{255.0 * clamped_depth, 0.0, 255.0 * (1.0 - clamped_depth)};
      cv::circle(visualization_image, point, proj_point.radius, color, 2);
    } else {
      cv::rectangle(visualization_image, cv::Point2f(point.x - proj_point.radius, point.y - proj_point.radius),
                    cv::Point2f(point.x + proj_point.radius, point.y + proj_point.radius), cv::Scalar{255, 0, 0}, 1);
    }
  }

  // Add text overlays
  cv::putText(visualization_image, "Tracked " + std::to_string(projected_points.size()) + " points", cv::Point{10, 30},
              cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar{0, 255, 255}, 2);

  cv::putText(visualization_image,
              "Depth range: " + std::to_string(static_cast<int>(min_depth)) + "m - " +
                  std::to_string(static_cast<int>(max_depth)) + "m",
              cv::Point{10, 60}, cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar{0, 255, 255}, 1);

  cv::putText(visualization_image, "Frame: " + std::to_string(static_cast<int>(frame->image->t)), cv::Point{10, 90},
              cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar{0, 255, 255}, 1);

  cv::rectangle(visualization_image, cv::Point{10, 120}, cv::Point{200, 160}, cv::Scalar{0, 0, 0}, -1);
  cv::putText(visualization_image, "Red: Close, Blue: Far", cv::Point{15, 140}, cv::FONT_HERSHEY_SIMPLEX, 0.4,
              cv::Scalar{255, 255, 255}, 1);

  const std::string filename{output_path + "/visualization_frame_" + std::to_string(static_cast<int>(frame->image->t)) +
                             ".png"};
  cv::imwrite(filename, visualization_image);
}

}  // namespace rdvio::extra