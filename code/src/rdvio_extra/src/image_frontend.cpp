#include "rdvio/extra/image_frontend.hpp"

namespace rdvio::extra {

ImageFrontend::ImageFrontend(const cv::Mat& image, const double timestamp, const bool enable_timing)
    : enable_timing_{enable_timing}, image_{image}, t{timestamp} {}

size_t ImageFrontend::GetWidth() const { return image_.cols; }

size_t ImageFrontend::GetHeight() const { return image_.rows; }

void ImageFrontend::PreProcess(const ClaheConfig& config) {
  if (enable_timing_) {
    const auto start_time = std::chrono::steady_clock::now();
    PreProcessImpl(config);
    const auto end_time = std::chrono::steady_clock::now();
    const auto duration_ms = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time).count();
    std::cout << "[FrontendImpl] PreProcess took " << duration_ms << " ms.\n";
  } else {
    PreProcessImpl(config);
  }
}

void ImageFrontend::ReleaseImageBuffer() {
  if (enable_timing_) {
    const auto start_time = std::chrono::steady_clock::now();
    ReleaseImageBufferImpl();
    const auto end_time = std::chrono::steady_clock::now();
    const auto duration_ms = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time).count();
    std::cout << "[FrontendImpl] ReleaseImageBuffer took " << duration_ms << " ms.\n";
  } else {
    ReleaseImageBufferImpl();
  }
};

void ImageFrontend::DetectKeypoints(const size_t max_points, const double keypoint_distance,
                                    std::vector<vector<2>>& keypoints) {
  if (enable_timing_) {
    const auto start_time = std::chrono::steady_clock::now();
    DetectKeypointsImpl(max_points, keypoint_distance, keypoints);
    const auto end_time = std::chrono::steady_clock::now();
    const auto duration_ms = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time).count();
    std::cout << "[FrontendImpl] DetectKeypoints took " << duration_ms << " ms.\n";
  } else {
    DetectKeypointsImpl(max_points, keypoint_distance, keypoints);
  }
};

void ImageFrontend::TrackKeypoints(const ImageFrontend* const next_image, const std::vector<vector<2>>& curr_keypoints,
                                   std::vector<vector<2>>& next_keypoints, std::vector<char>& result_status) {
  if (enable_timing_) {
    const auto start_time = std::chrono::steady_clock::now();
    TrackKeypointsImpl(next_image, curr_keypoints, next_keypoints, result_status);
    const auto end_time = std::chrono::steady_clock::now();
    const auto duration_ms = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time).count();
    std::cout << "[FrontendImpl] TrackKeypoints took " << duration_ms << " ms.\n";
  } else {
    TrackKeypointsImpl(next_image, curr_keypoints, next_keypoints, result_status);
  }
};

}  // namespace rdvio::extra