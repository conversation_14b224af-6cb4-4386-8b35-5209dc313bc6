#include "rdvio/extra/camera.h"

#include <spl-camera-models/base.hpp>

namespace rdvio::extra {

Camera::Camera(const CameraConfig& config) : config_{config} {
  do_image_rectification_ = config_.do_image_rectification;

  camera_tracking_margin_ = config_.camera_tracking_margin;

  undistortion_handler_ = std::make_shared<spl::camera_models::UniversalDistortionHandler>(config_.intrinsics);

  std::cout << "Camera is Initialized" << std::endl;
}

std::vector<vector<2>> Camera::RectifyKeyPoints(const std::vector<vector<2>>& keypoints) const {
  if (keypoints.size() == 0) {
    return std::vector<vector<2>>(0);
  }

  return ConvertToDouble(undistortion_handler_->UndistortPixels(ConvertToFloat(keypoints)));
}

std::vector<vector<2>> Camera::UnrectifyKeyPoints(const std::vector<vector<2>>& keypoints) const {
  if (keypoints.size() == 0) {
    return std::vector<vector<2>>(0);
  }

  return ConvertToDouble(undistortion_handler_->DistortPixels(ConvertToFloat(keypoints)));
}

size_t Camera::GetTrackingMargin() const { return camera_tracking_margin_; }

bool Camera::DoImageRectification() const { return do_image_rectification_; }

cv::Mat Camera::RectifyImage(const cv::Mat& image) const { return undistortion_handler_->UndistortImage(image); }

std::vector<Eigen::Vector2f> Camera::ConvertToFloat(const std::vector<vector<2>>& input) const {
  std::vector<Eigen::Vector2f> output;
  output.reserve(input.size());
  for (const auto& vec : input) {
    output.emplace_back(vec.cast<float>());
  }
  return output;
}

std::vector<vector<2>> Camera::ConvertToDouble(const std::vector<Eigen::Vector2f>& input) const {
  std::vector<vector<2>> output;
  output.reserve(input.size());
  for (const auto& vec : input) {
    output.emplace_back(vec.cast<double>());
  }
  return output;
}

}  // namespace rdvio::extra