#include "rdvio/extra/opencv_image.h"

#include <chrono>

#include "rdvio/extra/poisson_disk_filter.h"
#include "rdvio/extra/visualization_utils.hpp"
#include "rdvio/rdvio.hpp"
#include "rdvio/util.hpp"

namespace rdvio::extra {

OpenCvImage::OpenCvImage(const cv::Mat& image, const double timestamp, const std::shared_ptr<extra::Camera> camera,
                         const OpticalFlowConfig& optical_flow_config, bool enable_timing)
    : ImageFrontend{image, timestamp, enable_timing}, camera_{camera}, optical_flow_config_{optical_flow_config} {}
void OpenCvImage::DetectKeypointsImpl(const size_t max_points, const double keypoint_distance,
                                      std::vector<vector<2>>& keypoints) const {
  constexpr auto logger_name{"OpenCvImage::DetectKeypointsImpl"};
  std::vector<cv::KeyPoint> cv_keypoints;
  GoodFeaturesToTrack(max_points)->detect(image_, cv_keypoints);
  utils::LogStatistics(logger_name, "Number of detected feature points: {}", cv_keypoints.size());
  if (cv_keypoints.empty()) {
    spl::logging::Warning(logger_name, "Could not detect keypoints!");
    return;
  }
  std::sort(cv_keypoints.begin(), cv_keypoints.end(),
            [](const auto& a, const auto& b) { return a.response > b.response; });

  std::vector<vector<2>> new_keypoints;
  std::transform(cv_keypoints.begin(), cv_keypoints.end(), std::back_inserter(new_keypoints),
                 [](const cv::KeyPoint& kp) { return vector<2>{kp.pt.x, kp.pt.y}; });

  if (not camera_->DoImageRectification()) {
    new_keypoints = camera_->RectifyKeyPoints(new_keypoints);
  }

  PoissonDiskFilter<2> filter(keypoint_distance);
  filter.preset_points(keypoints);
  filter.insert_points(new_keypoints);

  utils::LogStatistics(logger_name, "Number of detected feature points after Poisson disk filter: {}",
                       new_keypoints.size());

  const size_t statistics_num_points_before_margin{new_keypoints.size()};

  const size_t margin{camera_->GetTrackingMargin()};
  new_keypoints.erase(std::remove_if(new_keypoints.begin(), new_keypoints.end(),
                                     [margin, this](const auto& kp) {
                                       return kp.x() < margin || kp.y() < margin || kp.x() >= image_.cols - margin ||
                                              kp.y() >= image_.rows - margin;
                                     }),
                      new_keypoints.end());

  utils::LogStatistics(logger_name, "Deleted keypoints because of margin: {}, it was {}, now it is {}",
                       statistics_num_points_before_margin - new_keypoints.size(), statistics_num_points_before_margin,
                       new_keypoints.size());
  keypoints.insert(keypoints.end(), new_keypoints.begin(), new_keypoints.end());
  utils::LogStatistics(logger_name, "Total number of keypoints: {}", keypoints.size());
}

void OpenCvImage::TrackKeypointsImpl(const ImageFrontend* const next_image,
                                     const std::vector<vector<2>>& curr_keypoints,
                                     std::vector<vector<2>>& next_keypoints, std::vector<char>& result_status) const {
  constexpr auto logger_name{"OpenCvImage::TrackKeypointsImpl"};
  const bool image_is_rectified{camera_->DoImageRectification()};

  std::vector<cv::Point2f> curr_cv_points{
      ToOpencv(image_is_rectified ? curr_keypoints : camera_->UnrectifyKeyPoints(curr_keypoints))};

  const size_t keypoint_count{curr_keypoints.size()};
  utils::LogStatistics(logger_name, "Starting with {} keypoints", keypoint_count);

  const std::vector<cv::Point2f> next_cv_points{
      next_keypoints.empty()
          ? (next_keypoints.resize(keypoint_count), curr_cv_points)
          : ToOpencv(image_is_rectified ? next_keypoints : camera_->UnrectifyKeyPoints(next_keypoints))};

  const OpenCvImage* const next_cvimage{dynamic_cast<const OpenCvImage*>(next_image)};

  result_status.assign(keypoint_count, 0);

  if (next_cvimage and not curr_cv_points.empty()) {
    cv::Mat cvstatus, cverr;
    std::vector<cv::Point2f> predicted_cv_points;
    if (optical_flow_config_.draw_optical_flow) {
      // Store the predicted points by motion model (IMU) for visualization.
      // The next_cv_points will be overwritten by optical flow in the next step.
      predicted_cv_points = next_cv_points;
    }

    calcOpticalFlowPyrLK(image_pyramid, next_cvimage->image_pyramid, curr_cv_points, next_cv_points, cvstatus, cverr,
                         cv::Size(optical_flow_config_.window_size, optical_flow_config_.window_size),
                         optical_flow_config_.level_num,
                         cv::TermCriteria(cv::TermCriteria::COUNT + cv::TermCriteria::EPS,
                                          optical_flow_config_.max_iterations, optical_flow_config_.epsilon),
                         cv::OPTFLOW_USE_INITIAL_FLOW);

    const auto undistorted_next_cv_points{
        image_is_rectified ? next_cv_points : ToOpencv(camera_->RectifyKeyPoints(FromOpencv(next_cv_points)))};

    const auto undistorted_curr_cv_points{
        image_is_rectified ? curr_cv_points : ToOpencv(camera_->RectifyKeyPoints(FromOpencv(curr_cv_points)))};

    const size_t margin{camera_->GetTrackingMargin()};
    const size_t img_width{image_.cols - margin};
    const size_t img_height{image_.rows - margin};
    const double max_movement{image_.rows * optical_flow_config_.max_movement_factor};

    size_t statistics_forward_optical_flow_success{0};
    size_t statistics_margin_filtered{0};
    size_t statistics_movement_filtered{0};

    for (size_t i = 0; i < keypoint_count; ++i) {
      result_status[i] = cvstatus.at<uchar>(static_cast<int>(i));
      if (result_status[i]) {
        statistics_forward_optical_flow_success++;
      }

      const auto& pt{undistorted_next_cv_points[i]};

      const bool is_point_in_margin{pt.x < margin || pt.x >= img_width || pt.y < margin || pt.y >= img_height};
      const bool is_point_too_far{cv::norm(undistorted_next_cv_points[i] - undistorted_curr_cv_points[i]) >
                                  max_movement};

      if (is_point_in_margin) {
        result_status[i] = 0;
        statistics_margin_filtered++;
      } else if (is_point_too_far) {
        result_status[i] = 0;
        statistics_movement_filtered++;
      }
    }

    utils::LogStatistics(logger_name, "Forward optical flow successful for {}/{} points",
                         statistics_forward_optical_flow_success, keypoint_count);
    utils::LogStatistics(logger_name, "Filtered {} points due to margin constraints", statistics_margin_filtered);
    utils::LogStatistics(logger_name, "Filtered {} points due to excessive movement (max: {:.2f} pixels)",
                         statistics_movement_filtered, max_movement);

    // Reverse tracking verification
    std::vector<uchar> reverse_status;
    std::vector<float> reverse_err;
    std::vector<cv::Point2f> reverse_pts{curr_cv_points};

    calcOpticalFlowPyrLK(next_cvimage->image_pyramid, image_pyramid, next_cv_points, reverse_pts, reverse_status,
                         reverse_err, cv::Size(optical_flow_config_.window_size, optical_flow_config_.window_size),
                         optical_flow_config_.level_num,
                         cv::TermCriteria(cv::TermCriteria::COUNT + cv::TermCriteria::EPS,
                                          optical_flow_config_.max_iterations, optical_flow_config_.epsilon),
                         cv::OPTFLOW_USE_INITIAL_FLOW);

    const auto undistorted_reverse_pts{
        image_is_rectified ? reverse_pts : ToOpencv(camera_->RectifyKeyPoints(FromOpencv(reverse_pts)))};

    size_t statistics_reverse_filtered{0};
    size_t statistics_cross_check_filtered{0};

    for (size_t i = 0; i < keypoint_count; ++i) {
      const bool is_distance_bigger_than_cross_check{
          cv::norm(undistorted_curr_cv_points[i] - undistorted_reverse_pts[i]) >
          optical_flow_config_.cross_check_error_th};
      if (result_status[i]) {
        if (not reverse_status[i]) {
          result_status[i] = 0;
          statistics_reverse_filtered++;
        } else if (is_distance_bigger_than_cross_check) {
          result_status[i] = 0;
          statistics_cross_check_filtered++;
        }
      }
    }

    utils::LogStatistics(logger_name, "Filtered {} points due to reverse tracking failure",
                         statistics_reverse_filtered);

    const double statistics_cross_check_percentage{
        keypoint_count > 0 ? (100.0 * statistics_cross_check_filtered / keypoint_count) : 0.0};
    utils::LogStatistics(logger_name, "Filtered {} points due to cross-check error (threshold: {:.2f}, {:.1f}%)",
                         statistics_cross_check_filtered, optical_flow_config_.cross_check_error_th,
                         statistics_cross_check_percentage);

    if (statistics_cross_check_percentage > optical_flow_config_.cross_check_error_rate_warning_threshold) {
      spl::logging::Warning(logger_name,
                            "High cross-check error rate detected! {:.1f}% ({}/{}) of points "
                            "failed cross-check validation (threshold: {:.2f})",
                            statistics_cross_check_percentage, statistics_cross_check_filtered, keypoint_count,
                            optical_flow_config_.cross_check_error_rate_warning_threshold);
    }

    if (optical_flow_config_.draw_optical_flow) {
      rdvio::extra::DrawOpticalFlow(image_, next_cvimage->image_, next_cvimage->t, undistorted_curr_cv_points,
                                    undistorted_next_cv_points, predicted_cv_points, result_status,
                                    optical_flow_config_.optical_flow_output_path);
    }
  }

  // Lambda to update keypoints
  auto update_keypoints = [&](const auto& source_points) {
    for (size_t i = 0; i < keypoint_count; ++i) {
      if (result_status[i]) {
        next_keypoints[i] = {source_points[i].x(), source_points[i].y()};
      }
    }
  };

  // Apply the transformation based on rectification status
  image_is_rectified ? update_keypoints(FromOpencv(next_cv_points))
                     : update_keypoints(camera_->RectifyKeyPoints(FromOpencv(next_cv_points)));

  // Count final successful tracks
  const size_t successful_tracks = std::count(result_status.begin(), result_status.end(), 1);
  utils::LogStatistics(logger_name, "Final result - successfully tracked {}/{} keypoints ({:.1f}%)", successful_tracks,
                       keypoint_count, keypoint_count > 0 ? (100.0 * successful_tracks / keypoint_count) : 0.0);
}

void OpenCvImage::PreProcessImpl(const ClaheConfig& config) {
  Clahe(config.clip_limit, config.width, config.height)->apply(image_, image_);
  image_pyramid.clear();
  buildOpticalFlowPyramid(image_, image_pyramid,
                          cv::Size(optical_flow_config_.window_size, optical_flow_config_.window_size),
                          optical_flow_config_.level_num, optical_flow_config_.reuse_input_image);
}

cv::CLAHE* OpenCvImage::Clahe(const double clip_limit, const int width, const int height) {
  static cv::Ptr<cv::CLAHE> s_clahe{cv::createCLAHE(clip_limit, cv::Size(width, height))};
  return s_clahe.get();
}

cv::GFTTDetector* OpenCvImage::GoodFeaturesToTrack(const size_t max_points) {
  static cv::Ptr<cv::GFTTDetector> s_gftt{cv::GFTTDetector::create(max_points, gftt_quality_level, gftt_min_distance,
                                                                   gftt_block_size, gftt_use_harris_detector)};
  return s_gftt.get();
}

void OpenCvImage::ReleaseImageBufferImpl() {
  image_.release();
  image_pyramid.clear();
}

std::vector<cv::Point2f> ToOpencv(const std::vector<vector<2>>& v) {
  std::vector<cv::Point2f> r(v.size());
  for (size_t i = 0; i < v.size(); ++i) {
    r[i].x = static_cast<float>(v[i].x());
    r[i].y = static_cast<float>(v[i].y());
  }
  return r;
}

std::vector<vector<2>> FromOpencv(const std::vector<cv::Point2f>& v) {
  std::vector<vector<2>> r(v.size());
  for (size_t i = 0; i < v.size(); ++i) {
    r[i].x() = static_cast<float>(v[i].x);
    r[i].y() = static_cast<float>(v[i].y);
  }
  return r;
}

}  // namespace rdvio::extra
