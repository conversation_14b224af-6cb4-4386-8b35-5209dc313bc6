#pragma once

#include <rdvio/estimation/state.h>
#include <rdvio/types.h>

namespace rdvio {

class Frame;

struct PreIntegrator {
  struct Delta {
    double t;
    quaternion q;
    vector<3> p;
    vector<3> v;
    matrix<15> cov;  // ordered in q, p, v, bg, ba
    matrix<15> sqrt_inv_cov;
  };

  struct Jacobian {
    matrix<3> dq_dbg;
    matrix<3> dp_dbg;
    matrix<3> dp_dba;
    matrix<3> dv_dbg;
    matrix<3> dv_dba;
  };

  bool integrate(const double t, const vector<3>& bg, const vector<3>& ba, const bool compute_jacobian,
                 const bool compute_covariance);

  void predict(const Frame* const old_frame, Frame* const new_frame);

  matrix<3> cov_w;  // continuous noise covariance
  matrix<3> cov_a;
  matrix<3> cov_bg;  // continuous random walk noise covariance
  matrix<3> cov_ba;

  Delta delta;
  Jacobian jacobian;

  std::vector<ImuData> data;

 private:
  void reset();

  void increment(const double dt, const ImuData& data, const vector<3>& bg, const vector<3>& ba,
                 const bool compute_jacobian, const bool compute_covariance);

  void compute_sqrt_inv_cov();
};

}  // namespace rdvio
