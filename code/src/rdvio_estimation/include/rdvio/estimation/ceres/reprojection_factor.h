#pragma once

#include <ceres/ceres.h>

#include "rdvio/geometry/lie_algebra.h"
#include "rdvio/geometry/stereo.h"
#include "rdvio/map/track.h"
#include "rdvio/types.h"

namespace rdvio {

class ReprojectionErrorFactor : public ceres::SizedCostFunction<2, 4, 3, 4, 3, 1> {
 public:
  ReprojectionErrorFactor(Frame* frame, Track* track)
      : frame(frame), track(track), keypoint_index(track->GetKeypointIndex(frame)) {
    const vector<3>& bearing{frame->GetBearing(keypoint_index)};
    local_tangent.leftCols<2>() = s2_tangential_basis(bearing);
    local_tangent.rightCols<1>() = bearing;
  }

  bool Evaluate(const double* const* parameters, double* residuals, double** jacobians) const override {
    const_map<quaternion> body_target_q{parameters[0]};
    const_map<vector<3>> body_target_p{parameters[1]};
    const_map<quaternion> body_reference_q{parameters[2]};
    const_map<vector<3>> body_reference_p{parameters[3]};
    const double& point_inv_depth{*parameters[4]};

    const auto& [frame_reference, keypoint_index_reference] = track->FirstKeypoint();
    const vector<3>& point_bearing_reference{frame_reference->GetBearing(keypoint_index_reference)};

    const ExtrinsicParams& camera_reference{frame_reference->camera};
    const ExtrinsicParams& camera_target{frame->camera};

    // Project point to space (reference camera)
    const vector<3> point_camera_reference{point_bearing_reference / point_inv_depth};

    // Project point from reference camera to body frame
    const vector<3> point_body_reference{camera_reference.Q_body_sensor * point_camera_reference +
                                         camera_reference.P_body_sensor};

    // Project point from reference body to world frame
    const vector<3> point_world{body_reference_q * point_body_reference + body_reference_p};

    // Project point from world to target body
    const vector<3> point_body_target{body_target_q.conjugate() * (point_world - body_target_p)};

    // Project point from target body to target camera
    const vector<3> point_camera_target{camera_target.Q_body_sensor.conjugate() *
                                        (point_body_target - camera_target.P_body_sensor)};

    // Project point on target camera tangent
    const vector<3> point_tangent_plane{local_tangent.transpose() * point_camera_target};

    const vector<2> reprojection_error{point_tangent_plane.hnormalized()};

    map<vector<2>> residual_vec{residuals};
    const matrix<2>& sqrt_inv_cov{frame->sqrt_inv_cov};
    residual_vec = sqrt_inv_cov * reprojection_error;

    if (jacobians) {
      const matrix<2, 3> dr_dpoint_tangent_plane{sqrt_inv_cov * dproj_dp(point_tangent_plane) *
                                                 local_tangent.transpose()};
      const matrix<2, 3> dr_dpoint_body_target{dr_dpoint_tangent_plane *
                                               camera_target.Q_body_sensor.conjugate().matrix()};
      const matrix<2, 3> dr_dpoint_world{dr_dpoint_body_target * body_target_q.conjugate().matrix()};
      const matrix<2, 3> dr_dpoint_body_reference{dr_dpoint_world * body_reference_q.matrix()};

      if (jacobians[0]) {
        map<matrix<2, 4, true>> dr_dbody_target_q{jacobians[0]};
        dr_dbody_target_q.block<2, 3>(0, 0) = dr_dpoint_body_target * hat(point_body_target);
        dr_dbody_target_q.col(3).setZero();
      }
      if (jacobians[1]) {
        map<matrix<2, 3, true>> dr_dbody_target_p{jacobians[1]};
        dr_dbody_target_p = -dr_dpoint_world;
      }
      if (jacobians[2]) {
        map<matrix<2, 4, true>> dr_dbody_reference_q{jacobians[2]};
        dr_dbody_reference_q.block<2, 3>(0, 0) = -dr_dpoint_body_reference * hat(point_body_reference);
        dr_dbody_reference_q.col(3).setZero();
      }
      if (jacobians[3]) {
        map<matrix<2, 3, true>> dr_dbody_reference_p{jacobians[3]};
        dr_dbody_reference_p = dr_dpoint_world;
      }
      if (jacobians[4]) {
        map<matrix<2, 1, true>> dr_dpoint_inv_depth{jacobians[4]};
        dr_dpoint_inv_depth = -dr_dpoint_body_reference * camera_reference.Q_body_sensor.matrix() *
                              point_camera_reference / point_inv_depth;
      }
    }

    return true;
  };

  Frame* frame;
  Track* track;
  const size_t keypoint_index;

 private:
  matrix<3> local_tangent;
};

class ReprojectionPriorFactor : public ceres::SizedCostFunction<2, 4, 3> {
 public:
  ReprojectionPriorFactor(Frame* frame, Track* track) : rpefactor(frame, track) {}

  bool Evaluate(const double* const* parameters, double* residuals, double** jacobians) const override {
    std::array<const double*, 5> params = {
        parameters[0], parameters[1], rpefactor.track->FirstFrame()->pose.q.coeffs().data(),
        rpefactor.track->FirstFrame()->pose.p.data(), &(rpefactor.track->landmark.inv_depth)};
    if (jacobians) {
      std::array<double*, 5> jacobs = {jacobians[0], jacobians[1], nullptr, nullptr, nullptr};
      return rpefactor.Evaluate(params.data(), residuals, jacobs.data());
    } else {
      return rpefactor.Evaluate(params.data(), residuals, nullptr);
    }
  }

  ReprojectionErrorFactor rpefactor;
};

}  // namespace rdvio