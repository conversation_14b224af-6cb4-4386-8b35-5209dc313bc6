#pragma once

#include <ceres/ceres.h>
#include <ceres/manifold.h>

#include "rdvio/geometry/lie_algebra.h"
#include "rdvio/types.h"

namespace rdvio {

class QuaternionManifold : public ceres::Manifold {
 public:
  // Ambient space dimension: the "outer" dimension is 4 for a quaternion.
  int AmbientSize() const override { return 4; }

  // Tangent space dimension: 3 for SO(3).
  int TangentSize() const override { return 3; }

  // -----------------------------------------------------------------------------
  // Plus: x_plus_delta = x * exp(delta), normalized
  // -----------------------------------------------------------------------------
  bool Plus(const double* x, const double* delta, double* x_plus_delta) const override {
    // Map the incoming pointers to Eigen quaternions and 3-vectors.
    // Quaternions are assumed to be [w, x, y, z].
    Eigen::Map<const Eigen::Quaterniond> q_in(x);
    Eigen::Map<const Eigen::Vector3d> delta_vec(delta);

    // Convert the 3D tangent vector into a small quaternion. The user previously
    // mentioned `expmap(const_map<vector<3>>(dq))`, so let's call that function
    // `ExpMap(delta_vec)`. We'll define it below or inline.
    Eigen::Quaterniond dq = ExpMap(delta_vec);

    // Multiply and normalize.
    Eigen::Quaterniond q_out = (q_in * dq).normalized();

    // Store in x_plus_delta.
    Eigen::Map<Eigen::Quaterniond> q_plus(x_plus_delta);
    q_plus = q_out;

    return true;
  }

  // -----------------------------------------------------------------------------
  // PlusJacobian: derivative of Plus w.r.t. the tangent delta
  //   - Produces a 4 x 3 matrix in row-major layout.
  // -----------------------------------------------------------------------------
  bool PlusJacobian(const double* x, double* jacobian) const override {
    // If you previously returned an identity-like 4x3 in the old interface,
    // it usually meant you were deferring the chain rule to some other code
    // (like "PreIntegrationError::Evaluate()").
    //
    // You can replicate that by setting the top-left 3x3 block to identity
    // and the bottom row to zero, e.g.:
    //    [1  0  0
    //     0  1  0
    //     0  0  1
    //     0  0  0]
    //
    // This is a simplistic approach. A more proper derivative can be computed
    // from the quaternion update, but if your code has always assumed a pass-through,
    // just do it consistently.
    Eigen::Map<Eigen::Matrix<double, 4, 3, Eigen::RowMajor>> J(jacobian);
    J.setZero();
    J.block<3, 3>(0, 0).setIdentity();
    return true;
  }

  // -----------------------------------------------------------------------------
  // Minus: map two points y, x on the manifold to the tangent space, y_minus_x
  //   - Typically: y_minus_x = log( x⁻¹ * y )
  // -----------------------------------------------------------------------------
  bool Minus(const double* y, const double* x, double* y_minus_x) const override {
    // For orientation difference, we often do:
    //   y_minus_x = log( x.inverse() * y ).
    // That yields a 3D tangent vector. If you do not *need* the "Minus" logic
    // in your optimization or cost terms, you can implement a placeholder,
    // but Ceres 2.x requires it anyway (pure virtual).
    Eigen::Map<const Eigen::Quaterniond> qx(x);
    Eigen::Map<const Eigen::Quaterniond> qy(y);

    // We do x.conjugate() * y (assuming unit quaternion).
    Eigen::Quaterniond diff = qx.conjugate() * qy;

    // Apply log map to get a 3D so(3) vector.
    Eigen::Vector3d log_vec = LogMap(diff);

    Eigen::Map<Eigen::Vector3d> out(y_minus_x);
    out = log_vec;
    return true;
  }

  // -----------------------------------------------------------------------------
  // MinusJacobian: derivative of Minus w.r.t. x
  //   - Produces a 3 x 4 matrix in row-major layout.
  // -----------------------------------------------------------------------------
  bool MinusJacobian(const double* x, double* jacobian) const override {
    // Like PlusJacobian, the correct derivative depends on your LogMap.
    // As a placeholder, you could do a block that sets zero or identity-like structure,
    // but for a fully correct approach, you should differentiate log(x⁻¹ * y) wrt x.
    //
    // We'll just zero it out here if you don't specifically need it:
    Eigen::Map<Eigen::Matrix<double, 3, 4, Eigen::RowMajor>> J(jacobian);
    J.setZero();
    return true;
  }

 private:
  //----------------------------------------------------------------------------
  // Example exponential map from so(3) to unit quaternion
  //----------------------------------------------------------------------------
  Eigen::Quaterniond ExpMap(const Eigen::Vector3d& v) const {
    // This is just one standard formula for an exponential map on SO(3).
    // If ||v|| < eps, you could do a first-order approximation. But here's
    // a typical approach for moderate angles:

    double theta = v.norm();
    if (theta < 1e-12) {
      // Very small angle => near identity
      return Eigen::Quaterniond(1.0, 0.0, 0.0, 0.0);
    } else {
      double half_theta = 0.5 * theta;
      double sin_half = std::sin(half_theta);
      double w = std::cos(half_theta);
      double xyz = sin_half / theta;
      return Eigen::Quaterniond(w, xyz * v.x(), xyz * v.y(), xyz * v.z());
    }
  }

  //----------------------------------------------------------------------------
  // Example logarithm map from a unit quaternion to so(3)
  //----------------------------------------------------------------------------
  Eigen::Vector3d LogMap(const Eigen::Quaterniond& q) const {
    // Ensure the quaternion is normalized.
    Eigen::Quaterniond qq = q.normalized();
    double w = qq.w();
    double xyz_norm = qq.vec().norm();

    double theta = 2.0 * std::atan2(xyz_norm, w);
    if (theta < 1e-12) {
      // Very small => near zero vector
      return Eigen::Vector3d::Zero();
    } else {
      // direction = q.vec() / ||q.vec()||
      // magnitude = theta
      double scale = theta / xyz_norm;
      return qq.vec() * scale;
    }
  }
};

}  // namespace rdvio
