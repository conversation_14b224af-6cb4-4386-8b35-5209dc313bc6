#pragma once

#include <rdvio/types.h>

namespace rdvio {

class Frame;
class Track;
class Map;

enum ErrorStateLocation { ES_Q = 0, ES_P = 3, ES_V = 6, ES_BG = 9, ES_BA = 12, ES_SIZE = 15 };

struct ExtrinsicParams {
  // Note(v<PERSON><PERSON><PERSON>): We store the sensor-to-body transformation.
  quaternion Q_body_sensor;
  vector<3> P_body_sensor;
};

using PoseState = Pose;

struct MotionState {
  MotionState() {
    linear_velocity.setZero();
    angular_velocity.setZero();
    bias_gyroscope.setZero();
    bias_accelerometer.setZero();
  }

  vector<3> linear_velocity;
  vector<3> angular_velocity;
  // Bias in this case is an offset of the sensor values that can and will evolve over time.
  vector<3> bias_gyroscope;
  vector<3> bias_accelerometer;
};

struct KinematicState {
  double timestamp{0};
  PoseState pose;
  MotionState motion;

  KinematicState() = default;
  KinematicState(const double timestamp_, const PoseState& pose_, const MotionState& motion_)
      : timestamp(timestamp_), pose(pose_), motion(motion_) {}
};

struct LandmarkState {
  LandmarkState() {
    inv_depth = 0;
    reprojection_error = 0;
  }

  double inv_depth;
  double reprojection_error;
};

}  // namespace rdvio
