#pragma once

#include "rdvio/estimation/ceres/marginalization_factor.h"
#include "rdvio/estimation/ceres/preintegration_factor.h"
#include "rdvio/estimation/ceres/reprojection_factor.h"
#include "rdvio/estimation/ceres/rotation_factor.h"
#include "rdvio/types.h"

namespace rdvio {

class Map;
class Frame;
class Track;

class Solver {
  struct SolverDetails;
  Solver();

 public:
  ~Solver();

  static void Init(const SolverConfig& config);

  static std::unique_ptr<Solver> Create();

  static std::unique_ptr<ReprojectionErrorFactor> CreateReprojectionErrorFactor(Frame* frame, Track* track);
  static std::unique_ptr<ReprojectionPriorFactor> CreateReprojectionPriorFactor(Frame* frame, Track* track);
  static std::unique_ptr<RotationPriorFactor> CreateRotationPriorFactor(Frame* frame, Track* track);
  static std::unique_ptr<PreIntegrationErrorFactor> CreatePreintegrationErrorFactor(
      Frame* frame_i, Frame* frame_j, const PreIntegrator& preintegration);
  static std::unique_ptr<PreIntegrationPriorFactor> CreatePreintegrationPriorFactor(
      Frame* frame_i, Frame* frame_j, const PreIntegrator& preintegration);
  static std::unique_ptr<MarginalizationFactor> CreateMarginalizationFactor(Map* map);

  void AddFrameStates(Frame* frame, bool with_motion = true);
  void AddTrackStates(Track* track);

  void AddFactor(ReprojectionErrorFactor* rpecost);
  void AddFactor(ReprojectionPriorFactor* rppcost);
  void AddFactor(RotationPriorFactor* ropcost);
  void AddFactor(PreIntegrationErrorFactor* piecost);
  void AddFactor(PreIntegrationPriorFactor* pipcost);
  void AddFactor(MarginalizationFactor* marcost);

  template <typename T>
  void PutFactor(std::unique_ptr<T>&& factor) {
    AddFactor(factor.get());
    ManageFactor(std::move(factor));
  }

  bool solve(bool verbose = false, bool use_optim = false);

 protected:
  void ManageFactor(std::unique_ptr<ReprojectionErrorFactor>&& factor);
  void ManageFactor(std::unique_ptr<ReprojectionPriorFactor>&& factor);
  void ManageFactor(std::unique_ptr<RotationPriorFactor>&& factor);
  void ManageFactor(std::unique_ptr<PreIntegrationErrorFactor>&& factor);
  void ManageFactor(std::unique_ptr<PreIntegrationPriorFactor>&& factor);
  void ManageFactor(std::unique_ptr<MarginalizationFactor>&& factor);
  std::unique_ptr<SolverDetails> details;

  inline static SolverConfig config_;
};

}  // namespace rdvio
