
#include "rdvio/estimation/solver.h"

#include <mutex>

#include <ceres/ceres.h>
#include <ceres/types.h>

#include "rdvio/estimation/ceres/marginalization_factor.h"
#include "rdvio/estimation/ceres/preintegration_factor.h"
#include "rdvio/estimation/ceres/quaternion_manifold.h"
#include "rdvio/estimation/ceres/reprojection_factor.h"
#include "rdvio/estimation/ceres/rotation_factor.h"
#include "rdvio/estimation/state.h"
#include "rdvio/map/frame.h"

namespace rdvio {

struct Solver::SolverDetails {
  std::vector<std::unique_ptr<ReprojectionErrorFactor>> managed_rpefactors;
  std::vector<std::unique_ptr<ReprojectionPriorFactor>> managed_rppfactors;
  std::vector<std::unique_ptr<RotationPriorFactor>> managed_ropfactors;
  std::vector<std::unique_ptr<PreIntegrationErrorFactor>> managed_piefactors;
  std::vector<std::unique_ptr<PreIntegrationPriorFactor>> managed_pipfactors;
  std::vector<std::unique_ptr<MarginalizationFactor>> managed_marfactors;
  std::unique_ptr<ceres::CauchyLoss> cauchy_loss;
  std::unique_ptr<QuaternionManifold> quaternion_manifold;
  // Declare problem last to ensure it's destroyed first
  std::unique_ptr<ceres::Problem> problem;
};

Solver::Solver() : details(std::make_unique<SolverDetails>()) {
  ceres::Problem::Options problem_options;
  problem_options.cost_function_ownership = ceres::DO_NOT_TAKE_OWNERSHIP;
  problem_options.loss_function_ownership = ceres::DO_NOT_TAKE_OWNERSHIP;
  problem_options.manifold_ownership = ceres::DO_NOT_TAKE_OWNERSHIP;

  details->problem = std::make_unique<ceres::Problem>(problem_options);
  details->cauchy_loss = std::make_unique<ceres::CauchyLoss>(1.0);  // TODO(jinyu): make configurable
  details->quaternion_manifold = std::make_unique<QuaternionManifold>();
}

Solver::~Solver() = default;

void Solver::Init(const SolverConfig& config) { config_ = config; }

std::unique_ptr<Solver> Solver::Create() { return std::unique_ptr<Solver>(new Solver()); }

std::unique_ptr<ReprojectionErrorFactor> Solver::CreateReprojectionErrorFactor(Frame* frame, Track* track) {
  return std::make_unique<ReprojectionErrorFactor>(frame, track);
}

std::unique_ptr<ReprojectionPriorFactor> Solver::CreateReprojectionPriorFactor(Frame* frame, Track* track) {
  return std::make_unique<ReprojectionPriorFactor>(frame, track);
}

std::unique_ptr<RotationPriorFactor> Solver::CreateRotationPriorFactor(Frame* frame, Track* track) {
  return std::make_unique<RotationPriorFactor>(frame, track);
}

std::unique_ptr<PreIntegrationErrorFactor> Solver::CreatePreintegrationErrorFactor(
    Frame* frame_i, Frame* frame_j, const PreIntegrator& preintegration) {
  return std::make_unique<PreIntegrationErrorFactor>(frame_i, frame_j, preintegration);
}

std::unique_ptr<PreIntegrationPriorFactor> Solver::CreatePreintegrationPriorFactor(
    Frame* frame_i, Frame* frame_j, const PreIntegrator& preintegration) {
  return std::make_unique<PreIntegrationPriorFactor>(frame_i, frame_j, preintegration);
}

std::unique_ptr<MarginalizationFactor> Solver::CreateMarginalizationFactor(Map* map) {
  return std::make_unique<MarginalizationFactor>(map);
}

void Solver::AddFrameStates(Frame* frame, bool with_motion) {
  details->problem->AddParameterBlock(frame->pose.q.coeffs().data(), 4, details->quaternion_manifold.get());
  details->problem->AddParameterBlock(frame->pose.p.data(), 3);
  if (frame->tag(FT_FIX_POSE)) {
    details->problem->SetParameterBlockConstant(frame->pose.q.coeffs().data());
    details->problem->SetParameterBlockConstant(frame->pose.p.data());
  }
  if (with_motion) {
    details->problem->AddParameterBlock(frame->motion.linear_velocity.data(), 3);
    details->problem->AddParameterBlock(frame->motion.bias_gyroscope.data(), 3);
    details->problem->AddParameterBlock(frame->motion.bias_accelerometer.data(), 3);
    if (frame->tag(FT_FIX_MOTION)) {
      details->problem->SetParameterBlockConstant(frame->motion.linear_velocity.data());
      details->problem->SetParameterBlockConstant(frame->motion.bias_gyroscope.data());
      details->problem->SetParameterBlockConstant(frame->motion.bias_accelerometer.data());
    }
  }
}

void Solver::AddTrackStates(Track* track) { details->problem->AddParameterBlock(&(track->landmark.inv_depth), 1); }

void Solver::AddFactor(ReprojectionErrorFactor* rpefactor) {
  details->problem->AddResidualBlock(
      rpefactor, details->cauchy_loss.get(), rpefactor->frame->pose.q.coeffs().data(), rpefactor->frame->pose.p.data(),
      rpefactor->track->FirstFrame()->pose.q.coeffs().data(), rpefactor->track->FirstFrame()->pose.p.data(),
      &(rpefactor->track->landmark.inv_depth));
}

void Solver::AddFactor(ReprojectionPriorFactor* rppfactor) {
  details->problem->AddResidualBlock(rppfactor, details->cauchy_loss.get(),
                                     rppfactor->rpefactor.frame->pose.q.coeffs().data(),
                                     rppfactor->rpefactor.frame->pose.p.data());
}

void Solver::AddFactor(RotationPriorFactor* ropfactor) {
  details->problem->AddResidualBlock(ropfactor, details->cauchy_loss.get(), ropfactor->frame->pose.q.coeffs().data());
}

void Solver::AddFactor(PreIntegrationErrorFactor* piefactor) {
  details->problem->AddResidualBlock(
      piefactor, nullptr, piefactor->frame_i->pose.q.coeffs().data(), piefactor->frame_i->pose.p.data(),
      piefactor->frame_i->motion.linear_velocity.data(), piefactor->frame_i->motion.bias_gyroscope.data(),
      piefactor->frame_i->motion.bias_accelerometer.data(), piefactor->frame_j->pose.q.coeffs().data(),
      piefactor->frame_j->pose.p.data(), piefactor->frame_j->motion.linear_velocity.data(),
      piefactor->frame_j->motion.bias_gyroscope.data(), piefactor->frame_j->motion.bias_accelerometer.data());
}

void Solver::AddFactor(PreIntegrationPriorFactor* pipfactor) {
  details->problem->AddResidualBlock(pipfactor, nullptr, pipfactor->piefactor.frame_j->pose.q.coeffs().data(),
                                     pipfactor->piefactor.frame_j->pose.p.data(),
                                     pipfactor->piefactor.frame_j->motion.linear_velocity.data(),
                                     pipfactor->piefactor.frame_j->motion.bias_gyroscope.data(),
                                     pipfactor->piefactor.frame_j->motion.bias_accelerometer.data());
}

void Solver::AddFactor(MarginalizationFactor* factor) {
  std::vector<double*> params;
  for (size_t i = 0; i < factor->linearization_frames().size(); ++i) {
    Frame* frame = factor->linearization_frames()[i];
    params.emplace_back(frame->pose.q.coeffs().data());
    params.emplace_back(frame->pose.p.data());
    params.emplace_back(frame->motion.linear_velocity.data());
    params.emplace_back(frame->motion.bias_gyroscope.data());
    params.emplace_back(frame->motion.bias_accelerometer.data());
  }
  details->problem->AddResidualBlock(static_cast<MarginalizationFactor*>(factor), nullptr, params);
}

bool Solver::solve(bool verbose, bool use_optim) {
  ceres::Solver::Options solver_options;
  ceres::Solver::Summary solver_summary;

  auto problem = details->problem.get();

  // Initially we also could use CUDA for the SfM - however on x86 Eigen remains faster.
  // TODO(vkallenbach): Use CUDA for embedded as its likley much faster.(problem->NumParameters();)
  solver_options.num_threads = config_.num_threads;
  solver_options.max_num_iterations = config_.max_num_iterations;
  solver_options.max_solver_time_in_seconds = config_.max_solver_time_in_seconds;
  solver_options.function_tolerance = config_.function_tolerance;
  solver_options.linear_solver_type = ceres::SPARSE_SCHUR;
  solver_options.trust_region_strategy_type = ceres::DOGLEG;
  solver_options.update_state_every_iteration = false;
  solver_options.use_mixed_precision_solves = false;
  solver_options.minimizer_progress_to_stdout = false;           // Prints progress and timing
  solver_options.logging_type = ceres::PER_MINIMIZER_ITERATION;  // Detailed logging per iteration
  solver_options.sparse_linear_algebra_library_type = ceres::EIGEN_SPARSE;

  ceres::Solve(solver_options, problem, &solver_summary);
  return solver_summary.IsSolutionUsable();
}

void Solver::ManageFactor(std::unique_ptr<ReprojectionErrorFactor>&& factor) {
  details->managed_rpefactors.emplace_back(std::move(factor));
}

void Solver::ManageFactor(std::unique_ptr<ReprojectionPriorFactor>&& factor) {
  details->managed_rppfactors.emplace_back(std::move(factor));
}

void Solver::ManageFactor(std::unique_ptr<RotationPriorFactor>&& factor) {
  details->managed_ropfactors.emplace_back(std::move(factor));
}

void Solver::ManageFactor(std::unique_ptr<PreIntegrationErrorFactor>&& factor) {
  details->managed_piefactors.emplace_back(std::move(factor));
}

void Solver::ManageFactor(std::unique_ptr<PreIntegrationPriorFactor>&& factor) {
  details->managed_pipfactors.emplace_back(std::move(factor));
}

void Solver::ManageFactor(std::unique_ptr<MarginalizationFactor>&& factor) {
  details->managed_marfactors.emplace_back(std::move(factor));
}

}  // namespace rdvio