#include <gtest/gtest.h>

#include <ceres/ceres.h>
#include <rdvio/estimation/ceres/reprojection_factor.h>
#include <rdvio/testing_util/unit/construct_map_fixture.hpp>

using namespace rdvio;

class ReprojectionFactorTest : public ConstructMapFixture {
 protected:
  // Retrieve the first two frames from the map. reference_frame_ (10m) and target_frame_ (8m)
  ReprojectionFactorTest()
      : reference_frame_{map_->get_frame(0)},
        target_frame_{map_->get_frame(1)},
        track_{reference_frame_->get_track(0, map_.get())} {}

  Frame* reference_frame_;
  Frame* target_frame_;
  Track* track_;
};

TEST_F(ReprojectionFactorTest, EvaluateResidualsAndJacobian) {
  const quaternion q_target{target_frame_->pose.q};
  const vector<3> p_target{target_frame_->pose.p};
  const quaternion q_reference{reference_frame_->pose.q};
  const vector<3> p_reference{reference_frame_->pose.p};
  const double inv_depth{track_->landmark.inv_depth};

  const std::array<const double* const, 5> parameters{q_target.coeffs().data(), p_target.data(),
                                                      q_reference.coeffs().data(), p_reference.data(), &inv_depth};

  std::array<double, 2> residuals{0.0, 0.0};
  std::array<std::array<double, 8>, 5> jacobians{{0.0}};
  std::array<double*, 5> jacobians_ptrs{jacobians[0].data(), jacobians[1].data(), jacobians[2].data(),
                                        jacobians[3].data(), jacobians[4].data()};

  const ReprojectionErrorFactor factor{reference_frame_, track_};
  const bool success{factor.Evaluate(parameters.data(), residuals.data(), jacobians_ptrs.data())};
  ASSERT_TRUE(success);

  EXPECT_NEAR(residuals[0], 0.0, 1e-6);
  EXPECT_NEAR(residuals[1], 0.0, 1e-6);

  for (int i = 0; i < 5; ++i) {
    for (int j = 0; j < 8; ++j) {
      EXPECT_NEAR(jacobians[i][j], 0.0, 1e-6) << "Jacobian " << i << ", " << j << " is not zero.";
    }
  }
}