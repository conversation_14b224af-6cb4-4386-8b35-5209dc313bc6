#include <gtest/gtest.h>

#include <array>

#include <ceres/ceres.h>
#include <rdvio/estimation/ceres/marginalization_factor.h>
#include <rdvio/map/frame.h>
#include <rdvio/map/map.h>
#include <rdvio/testing_util/unit/construct_map_fixture.hpp>
#include <rdvio/types.h>

using namespace rdvio;

class TestMarginalizationFactor : public MarginalizationFactor {
 public:
  explicit TestMarginalizationFactor(Map* map) : MarginalizationFactor(map) {}
  ~TestMarginalizationFactor() override = default;

  // Available in abstract base class MarginalizationFactor
  auto get_frames() const { return frames; }
  auto get_pose_linear_point() const { return pose_linearization_point; }
  auto get_motion_linear_point() const { return motion_linearization_point; }
  auto get_infovec() const { return infovec; }
  auto get_sqrt_inv_cov() const { return sqrt_inv_cov; }
  Map* get_base_map() const { return base_map; }

  // Expose parameter block sizes and number of residuals from ceresMarginalization
  auto get_parameter_block_sizes() const { return parameter_block_sizes(); }
  auto get_num_residuals() const { return num_residuals(); }
};

class MarginalizationFactorTest : public ConstructMapFixture {
 protected:
  MarginalizationFactorTest() {}
};

TEST_F(MarginalizationFactorTest, ConstructorInitialization) {
  const size_t num_linearization_frames{map_->frame_num() - 1};
  const size_t state_dim{ES_SIZE * num_linearization_frames};

  TestMarginalizationFactor factor(map_.get());

  ASSERT_EQ(factor.get_base_map(), map_.get());

  ASSERT_EQ(factor.get_frames().size(), num_linearization_frames);
  ASSERT_EQ(factor.get_pose_linear_point().size(), num_linearization_frames);
  ASSERT_EQ(factor.get_motion_linear_point().size(), num_linearization_frames);

  for (size_t i{0}; i < num_linearization_frames; ++i) {
    const Frame* frame{map_->get_frame(i)};
    ASSERT_EQ(factor.get_frames()[i], frame);
    EXPECT_TRUE(factor.get_pose_linear_point()[i].q.isApprox(frame->pose.q));
    EXPECT_TRUE(factor.get_pose_linear_point()[i].p.isApprox(frame->pose.p));
    EXPECT_TRUE(factor.get_motion_linear_point()[i].linear_velocity.isApprox(frame->motion.linear_velocity));
    EXPECT_TRUE(factor.get_motion_linear_point()[i].bias_accelerometer.isApprox(frame->motion.bias_accelerometer));
    EXPECT_TRUE(factor.get_motion_linear_point()[i].bias_gyroscope.isApprox(frame->motion.bias_gyroscope));
  }

  ASSERT_EQ(factor.get_infovec().size(), state_dim);
  EXPECT_TRUE(factor.get_infovec().isZero(1e-9));

  ASSERT_EQ(factor.get_sqrt_inv_cov().rows(), state_dim);
  ASSERT_EQ(factor.get_sqrt_inv_cov().cols(), state_dim);

  matrix<> expected_sqrt_inv_cov{matrix<>::Zero(state_dim, state_dim)};
  expected_sqrt_inv_cov.block<3, 3>(ES_P, ES_P) = 1.0e15 * matrix<3>::Identity();
  expected_sqrt_inv_cov.block<3, 3>(ES_Q, ES_Q) = 1.0e15 * matrix<3>::Identity();

  EXPECT_TRUE(factor.get_sqrt_inv_cov().isApprox(expected_sqrt_inv_cov, 1e-9));
}

TEST_F(MarginalizationFactorTest, ParameterBlockSizeTest) {
  const size_t num_linearization_frames{map_->frame_num() - 1};
  const size_t state_dim{ES_SIZE * num_linearization_frames};

  TestMarginalizationFactor factor{map_.get()};

  ASSERT_EQ(factor.get_num_residuals(), state_dim);

  const auto parameter_block_sizes{factor.get_parameter_block_sizes()};
  // Each frame has 5 parameter blocks: q, p, v, bg, ba
  ASSERT_EQ(parameter_block_sizes.size(), num_linearization_frames * 5);

  for (size_t i{0}; i < num_linearization_frames; ++i) {
    // Quaternion (q) has 4 parameters
    EXPECT_EQ(parameter_block_sizes[5 * i + 0], 4);
    // Position (p) has 3 parameters
    EXPECT_EQ(parameter_block_sizes[5 * i + 1], 3);
    // Velocity (v) has 3 parameters
    EXPECT_EQ(parameter_block_sizes[5 * i + 2], 3);
    // Gyroscope bias (bg) has 3 parameters
    EXPECT_EQ(parameter_block_sizes[5 * i + 3], 3);
    // Accelerometer bias (ba) has 3 parameters
    EXPECT_EQ(parameter_block_sizes[5 * i + 4], 3);
  }
}

TEST_F(MarginalizationFactorTest, EvaluateResidualsAndJacobians) {
  TestMarginalizationFactor factor{map_.get()};

  constexpr size_t kNumFrames{2};
  constexpr size_t kParamsPerFrame{5};
  constexpr size_t kTotalParams{kNumFrames * kParamsPerFrame};

  std::array<const double*, kTotalParams> parameters{};

  for (size_t i{0}; i < factor.get_frames().size(); ++i) {
    const Frame* const frame{factor.get_frames()[i]};
    parameters[5 * i + 0] = frame->pose.q.coeffs().data();
    parameters[5 * i + 1] = frame->pose.p.data();
    parameters[5 * i + 2] = frame->motion.linear_velocity.data();
    parameters[5 * i + 3] = frame->motion.bias_gyroscope.data();
    parameters[5 * i + 4] = frame->motion.bias_accelerometer.data();
  }

  std::array<double, 30> residuals{};

  // Allocate space for jacobians and set up pointers
  // q (4 params), p, v, bg, ba (3 params each)
  std::array<std::array<double, 30 * 4>, kNumFrames> q_jacobians{};
  std::array<std::array<double, 30 * 3>, kNumFrames> p_jacobians{};
  std::array<std::array<double, 30 * 3>, kNumFrames> v_jacobians{};
  std::array<std::array<double, 30 * 3>, kNumFrames> bg_jacobians{};
  std::array<std::array<double, 30 * 3>, kNumFrames> ba_jacobians{};

  std::array<double*, kTotalParams> jacobians_ptrs{};

  for (size_t i{0}; i < kNumFrames; ++i) {
    jacobians_ptrs[5 * i + 0] = q_jacobians[i].data();
    jacobians_ptrs[5 * i + 1] = p_jacobians[i].data();
    jacobians_ptrs[5 * i + 2] = v_jacobians[i].data();
    jacobians_ptrs[5 * i + 3] = bg_jacobians[i].data();
    jacobians_ptrs[5 * i + 4] = ba_jacobians[i].data();
  }

  const bool success{factor.Evaluate(parameters.data(), residuals.data(), jacobians_ptrs.data())};
  ASSERT_TRUE(success);

  // Since we're evaluating at the linearization point, residuals should be nearly zero
  for (const auto& residual : residuals) {
    EXPECT_NEAR(residual, 0.0, 1e-9);
  }
}

TEST_F(MarginalizationFactorTest, Marginalization) {
  TestMarginalizationFactor factor{map_.get()};

  const size_t original_num_frames{factor.get_frames().size()};
  const int original_num_residuals{factor.get_num_residuals()};
  const size_t original_num_param_blocks{factor.get_parameter_block_sizes().size()};

  factor.marginalize(0);

  // After examining the implementation of marginalize(),
  // we see that marginalization doesn't actually reduce the number of frames.
  // Instead, it reformulates the factor using a different structure.
  // The frames stay the same, but the linearization points change.

  EXPECT_EQ(factor.get_frames().size(), original_num_frames);
  EXPECT_EQ(factor.get_pose_linear_point().size(), original_num_frames);
  EXPECT_EQ(factor.get_motion_linear_point().size(), original_num_frames);

  EXPECT_EQ(factor.get_num_residuals(), original_num_residuals);

  EXPECT_EQ(factor.get_parameter_block_sizes().size(), original_num_param_blocks);

  EXPECT_EQ(factor.get_infovec().size(), original_num_residuals);
  EXPECT_EQ(factor.get_sqrt_inv_cov().rows(), original_num_residuals);
  EXPECT_EQ(factor.get_sqrt_inv_cov().cols(), original_num_residuals);

  EXPECT_FALSE(factor.get_infovec().isZero(1e-9));
}