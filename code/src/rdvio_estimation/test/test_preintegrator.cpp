#include <gtest/gtest.h>

#include <rdvio/estimation/preintegrator.h>

using namespace rdvio;

class PreIntegratorFixture : public ::testing::Test {
 protected:
  // cppcheck-suppress [unusedFunction, unmatchedSuppression]
  virtual void SetUp() {
    const ImuData data_1{0, {1, 1, 1}, {1, 1, 1}};
    pre_integrator_.data.push_back(data_1);
  }

  vector<3> rotation_bias_{0.0, 0.0, 0.0};
  vector<3> acceleration_bias_{0.0, 0.0, 0.0};
  PreIntegrator pre_integrator_{};
};

TEST_F(PreIntegratorFixture, AccelerationAndVelocity) {
  pre_integrator_.integrate(1, rotation_bias_, acceleration_bias_, false, false);

  ASSERT_FLOAT_EQ(pre_integrator_.delta.t, 1.0);

  // NOTE(Jack): We have linear acceleration in all three direction of 1 m/s². The change in position due to
  // acceleration is 0.5*a*t², which means that in one second we will have moved 0.5 along each axis of motion.
  ASSERT_FLOAT_EQ(pre_integrator_.delta.p[0], 0.5);
  ASSERT_FLOAT_EQ(pre_integrator_.delta.p[1], 0.5);
  ASSERT_FLOAT_EQ(pre_integrator_.delta.p[2], 0.5);

  // NOTE(Jack): Similar to the case before but now have linear 1m/s² acceleration, for one second, so we have a
  // velocity of 1m/s along each axis of motion.
  ASSERT_FLOAT_EQ(pre_integrator_.delta.v[0], 1.0);
  ASSERT_FLOAT_EQ(pre_integrator_.delta.v[1], 1.0);
  ASSERT_FLOAT_EQ(pre_integrator_.delta.v[2], 1.0);

  // TODO(Jack): I also think we should be able to test pre_integrator_.delta.q, but the values are all zeros, even
  // though it is supposed to be a normalized quaternion. I guess the test data is wrong?
}

TEST_F(PreIntegratorFixture, Jacobian) {
  pre_integrator_.integrate(1, rotation_bias_, acceleration_bias_, true, true);

  // NOTE(Jack): Honestly we cannot intuitively understand the jacobian, without a lot of extra effort. These are
  // heuristic values which we got by running the program once. They should never change unless the implementation
  // changes. We also do not test every value because it is not practical, here we select one value from each
  // subcomponent of the jacobian.
  ASSERT_FLOAT_EQ(pre_integrator_.jacobian.dq_dbg(0, 0), -0.71324009);
  ASSERT_FLOAT_EQ(pre_integrator_.jacobian.dp_dbg(0, 0), 0.0);
  ASSERT_FLOAT_EQ(pre_integrator_.jacobian.dp_dba(0, 0), -0.5);
  ASSERT_FLOAT_EQ(pre_integrator_.jacobian.dv_dbg(0, 0), 0.0);
  ASSERT_FLOAT_EQ(pre_integrator_.jacobian.dv_dba(0, 0), -1.0);

  // TODO(Jack): I also wanted to test the covariance, but all the values are zero (I think), and therefore I think I am
  // doing something wrong with the input data.
}