#include <gtest/gtest.h>

#include <array>
#include <cmath>

#include <Eigen/Core>
#include <Eigen/Geometry>
#include <rdvio/estimation/ceres/quaternion_manifold.h>

using namespace rdvio;

TEST(QuaternionManifoldTest, AmbientAndTangentSize) {
  const QuaternionManifold manifold{};

  EXPECT_EQ(4, manifold.AmbientSize());
  EXPECT_EQ(3, manifold.TangentSize());
}

TEST(QuaternionManifoldTest, PlusOperation) {
  const QuaternionManifold manifold{};

  // 60 degrees rotation around x-axis, 15 degrees around y-axis
  const std::array<double, 4> init{0.8586164, 0.4957224, 0.113039, 0.0652631};

  // Small rotation around x-axis and z-axis
  const std::array<double, 3> delta{0.0034907, 0.0, 0.0005236};
  std::array<double, 4> result{0.0, 0.0, 0.0, 0.0};

  const bool success{manifold.Plus(init.data(), delta.data(), result.data())};
  EXPECT_TRUE(success);

  EXPECT_NEAR(result[0], 0.85885878, 1e-6);
  EXPECT_NEAR(result[1], 0.49569415, 1e-6);
  EXPECT_NEAR(result[2], 0.11219070, 1e-6);
  EXPECT_NEAR(result[3], 0.06373482, 1e-6);
}

TEST(QuaternionManifoldTest, PlusJacobian) {
  const QuaternionManifold manifold{};

  const std::array<double, 4> init{0.8586164, 0.4957224, 0.113039, 0.0652631};
  std::array<double, 12> jacobian{0.0};

  const bool success{manifold.PlusJacobian(init.data(), jacobian.data())};
  EXPECT_TRUE(success);

  // Expected jacobian is identity in top-left 3x3 block and zeros in bottom row
  const std::array<double, 12> expected{1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0};

  for (int i{0}; i < 12; ++i) {
    EXPECT_NEAR(jacobian[i], expected[i], 1e-6) << "Jacobian element " << i << " doesn't match";
  }
}

TEST(QuaternionManifoldTest, MinusOperation) {
  const QuaternionManifold manifold{};

  const std::array<double, 4> init{0.8586164, 0.4957224, 0.113039, 0.0652631};

  // Rotation around y-axis
  const double angle{0.2};
  const double half_angle{angle / 2.0};
  const std::array<double, 4> delta{std::cos(half_angle), 0.0, std::sin(half_angle), 0.0};
  std::array<double, 3> result{0.0, 0.0, 0.0};

  const bool success{manifold.Minus(init.data(), delta.data(), result.data())};
  EXPECT_TRUE(success);

  EXPECT_NEAR(result[0], -0.03235770, 1e-6);
  EXPECT_NEAR(result[1], 0.05604518, 1e-6);
  EXPECT_NEAR(result[2], -1.04685155, 1e-6);
}

TEST(QuaternionManifoldTest, MinusJacobian) {
  const QuaternionManifold manifold{};

  const std::array<double, 4> init{0.8586164, 0.4957224, 0.113039, 0.0652631};
  std::array<double, 12> jacobian{1.0};

  const bool success{manifold.MinusJacobian(init.data(), jacobian.data())};
  EXPECT_TRUE(success);

  // Expected jacobian is all zeros as implemented
  for (int i{0}; i < 12; ++i) {
    EXPECT_NEAR(jacobian[i], 0.0, 1e-6) << "Jacobian element " << i << " is not zero";
  }
}

TEST(QuaternionManifoldTest, ComposedOperations) {
  const QuaternionManifold manifold{};

  // 60 degrees around x-axis
  const std::array<double, 4> init{0.866, 0.5, 0.0, 0.0};

  // Small rotation around y-axis
  const std::array<double, 3> delta{0.01, 0.2, 1.05};

  std::array<double, 4> result_plus{0.0, 0.0, 0.0, 0.0};
  EXPECT_TRUE(manifold.Plus(init.data(), delta.data(), result_plus.data()));

  std::array<double, 3> recovered_delta{0.0, 0.0, 0.0};
  EXPECT_TRUE(manifold.Minus(result_plus.data(), init.data(), recovered_delta.data()));

  // Verify recovered delta is close to original delta
  for (int i{0}; i < 3; ++i) {
    EXPECT_NEAR(recovered_delta[i], delta[i], 1e-6) << "Delta element " << i << " wasn't recovered correctly";
  }
}

TEST(QuaternionManifoldTest, ExpMapSmallAngle) {
  const QuaternionManifold manifold{};

  // Zero-rotation case using Plus with identity quaternion
  const std::array<double, 4> init{1.0, 0.0, 0.0, 0.0};
  const std::array<double, 3> delta{0.0, 0.0, 0.0};
  std::array<double, 4> result{0.0, 0.0, 0.0, 0.0};

  EXPECT_TRUE(manifold.Plus(init.data(), delta.data(), result.data()));

  EXPECT_NEAR(result[0], 1.0, 1e-6);
  EXPECT_NEAR(result[1], 0.0, 1e-6);
  EXPECT_NEAR(result[2], 0.0, 1e-6);
  EXPECT_NEAR(result[3], 0.0, 1e-6);
}