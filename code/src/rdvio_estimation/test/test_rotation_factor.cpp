#include <gtest/gtest.h>

#include <array>

#include <ceres/ceres.h>
#include <rdvio/estimation/ceres/rotation_factor.h>
#include <rdvio/testing_util/unit/construct_map_fixture.hpp>

using namespace rdvio;

class RotationPriorFactorTest : public ConstructMapFixture {
 protected:
  RotationPriorFactorTest() : target_frame_{map_->get_frame(1)}, track_{map_->get_frame(0)->get_track(0, map_.get())} {}

  Frame* target_frame_;
  Track* track_;
};

TEST_F(RotationPriorFactorTest, EvaluateResidualsAndJacobian) {
  const quaternion q_target{target_frame_->pose.q};
  const std::array<const double* const, 1> parameters{q_target.coeffs().data()};

  std::array<double, 2> residuals{0.0, 0.0};

  std::array<double, 8> jacobians{0.0};
  std::array<double*, 1> jacobians_ptrs{jacobians.data()};

  const RotationPriorFactor factor{target_frame_, track_};
  const bool success{factor.Evaluate(parameters.data(), residuals.data(), jacobians_ptrs.data())};
  ASSERT_TRUE(success);

  EXPECT_NEAR(residuals[0], 0.0, 1e-6);
  EXPECT_NEAR(residuals[1], 0.0, 1e-6);

  for (int i{0}; i < 8; ++i) {
    EXPECT_NEAR(jacobians[i], 0.0, 1e-6) << "Jacobian [" << i << "] is not zero.";
  }
}