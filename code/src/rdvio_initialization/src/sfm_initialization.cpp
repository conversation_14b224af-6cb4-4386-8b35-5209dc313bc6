#include "rdvio/initialization/sfm_initialization.h"

#include <spl-logging/logging.hpp>

#include "rdvio/estimation/solver.h"
#include "rdvio/geometry/stereo.h"
#include "rdvio/initialization/collect_keypoints.h"
#include "rdvio/initialization/pose_generation.h"
#include "rdvio/initialization/triangulation.h"
#include "rdvio/map/track.h"

namespace rdvio {

bool InitializeSfm(const SfmInitializerConfig& config, Map& map) {
  // [1] try initializing using raw_map
  Frame* init_frame_i = map.get_frame(0);
  Frame* init_frame_j = map.get_frame(map.frame_num() - 1);

  std::vector<vector<2>> frame_i_keypoints;
  std::vector<vector<2>> frame_j_keypoints;
  std::vector<std::pair<size_t, size_t>> init_matches;
  const bool good_keypoints{CollectKeypoints(init_frame_i, init_frame_j, config.min_matches, config.min_parallax,
                                             frame_i_keypoints, frame_j_keypoints, init_matches)};

  if (not good_keypoints) {
    spl::logging::Warning("InitializeSfm", "Not enough good kepypoints for initialization");
    return false;
  }

  std::vector<matrix<3>> Rs;
  std::vector<vector<3>> Ts;
  const bool hypothesis_generation_successful{
      GeneratePoseHypothesis(frame_i_keypoints, frame_j_keypoints, init_frame_i->K(0, 0), config.random, Rs, Ts)};
  if (not hypothesis_generation_successful) {
    spl::logging::Warning("InitializeSfm", "Hypothesis generation failed");
    return false;
  }

  // [1.1] triangulation
  auto [best_rt_index, triangulation_points, triangulation_status, triangulation_counts]{
      InitializerTriangulationProcess(Rs, Ts, frame_i_keypoints, frame_j_keypoints, config.min_triangulation)};

  const matrix<3> best_R{Rs[best_rt_index]};
  const vector<3> best_T{Ts[best_rt_index]};

  std::vector<vector<3>> init_points;
  std::vector<char> init_point_status;
  init_points.swap(triangulation_points[best_rt_index]);
  init_point_status.swap(triangulation_status[best_rt_index]);
  size_t triangulated_num = triangulation_counts[best_rt_index];

  if (triangulated_num < config.min_triangulation) {
    spl::logging::Warning("InitializeSfm", "Not enough triangulated points");
    return false;
  }

  // [2] create sfm map

  // [2.1] set init states
  PoseState pose;
  pose.q.setIdentity();
  pose.p.setZero();
  init_frame_i->set_pose(init_frame_i->camera, pose);
  pose.q = best_R.transpose();
  pose.p = -(best_R.transpose() * best_T);
  init_frame_j->set_pose(init_frame_j->camera, pose);

  for (size_t k = 0; k < init_points.size(); ++k) {
    if (init_point_status[k] == 0) {
      continue;
    }
    Track* track = init_frame_i->get_track(init_matches[k].first);
    track->SetLandmarkPoint(init_points[k]);
    track->tag(TT_VALID) = true;
    track->tag(TT_TRIANGULATED) = true;
  }

  // [2.2] solve other frames via pnp
  for (size_t j = 1; j + 1 < map.frame_num(); ++j) {
    Frame* frame_i = map.get_frame(j - 1);
    Frame* frame_j = map.get_frame(j);
    frame_j->set_pose(frame_j->camera, frame_i->get_pose(frame_i->camera));
    auto solver = Solver::Create();
    solver->AddFrameStates(frame_j);
    for (size_t k = 0; k < frame_j->keypoint_num(); ++k) {
      Track* track = frame_j->get_track(k);
      if (!track) {
        continue;
      }
      if (!track->HasKeypoint(map.get_frame(0))) {
        continue;
      }
      if (track->tag(TT_VALID) && track->tag(TT_TRIANGULATED)) {
        solver->PutFactor(Solver::CreateReprojectionPriorFactor(frame_j, track));
      }
    }
    solver->solve();
  }

  // [2.3] triangulate more points
  for (size_t i = 0; i < map.track_num(); ++i) {
    Track* track = map.get_track(i);
    if (track->tag(TT_VALID)) {
      continue;
    }
    if (auto p = track->Triangulate()) {
      track->SetLandmarkPoint(p.value());
      track->tag(TT_VALID) = true;
      track->tag(TT_TRIANGULATED) = true;
    }
  }

  // [3] sfm

  // [3.1] bundle adjustment
  map.get_frame(0)->tag(FT_FIX_POSE) = true;
  auto solver = Solver::Create();
  for (size_t i = 0; i < map.frame_num(); ++i) {
    solver->AddFrameStates(map.get_frame(i), false);
  }
  std::unordered_set<Track*> visited_tracks;
  for (size_t i = 0; i < map.frame_num(); ++i) {
    Frame* frame = map.get_frame(i);
    for (size_t j = 0; j < frame->keypoint_num(); ++j) {
      Track* track = frame->get_track(j);
      if (!track) {
        continue;
      }
      if (!track->tag(TT_VALID)) {
        continue;
      }
      if (visited_tracks.count(track) > 0) {
        continue;
      }
      visited_tracks.insert(track);
      solver->AddTrackStates(track);
    }
  }
  for (size_t i = 0; i < map.frame_num(); ++i) {
    Frame* frame = map.get_frame(i);
    for (size_t j = 0; j < frame->keypoint_num(); ++j) {
      Track* track = frame->get_track(j);
      if (!track) {
        continue;
      }
      if (!track->all_tagged(TT_VALID, TT_TRIANGULATED)) {
        continue;
      }
      if (frame == track->FirstFrame()) {
        continue;
      }
      solver->AddFactor(frame->reprojection_error_factors[j].get());
    }
  }
  if (!solver->solve()) {
    spl::logging::Warning("InitializeSfm", "Bundle adjustment optimization failed");
    return false;
  }

  spl::logging::Debug("InitializeSfm", "Map track count before outlier pruning {}", map.track_num());
  map.prune_tracks([](const Track* track) {
    return !track->tag(TT_VALID) || track->landmark.reprojection_error > 3.0;  // TODO: make configurable
  });
  spl::logging::Debug("InitializeSfm", "Map track count after outlier pruning {}", map.track_num());

  return true;
}

}  // namespace rdvio