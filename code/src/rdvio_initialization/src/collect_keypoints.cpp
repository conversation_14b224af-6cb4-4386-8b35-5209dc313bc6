#include <rdvio/geometry/stereo.h>
#include <rdvio/initialization/collect_keypoints.h>
#include <rdvio/map/track.h>
#include <spl-logging/logging.hpp>

namespace rdvio {

bool CollectKeypoints(const Frame* const init_frame_i, const Frame* const init_frame_j, const size_t min_matches,
                      const double min_parallax, std::vector<vector<2>>& frame_i_keypoints,
                      std::vector<vector<2>>& frame_j_keypoints, std::vector<std::pair<size_t, size_t>>& init_matches) {
  double total_parallax{0.0};
  int common_track_num{0};
  for (size_t ki = 0; ki < init_frame_i->keypoint_num(); ++ki) {
    Track* track = init_frame_i->get_track(ki);
    if (!track) {
      continue;
    }
    size_t kj = track->GetKeypointIndex(init_frame_j);
    if (kj == nil()) {
      continue;
    }

    frame_i_keypoints.push_back(init_frame_i->GetBearing(ki).hnormalized());
    frame_j_keypoints.push_back(init_frame_j->GetBearing(kj).hnormalized());
    init_matches.emplace_back(ki, kj);

    total_parallax += (PinholeProject(init_frame_i->GetBearing(ki), init_frame_i->K) -
                       PinholeProject(init_frame_j->GetBearing(kj), init_frame_j->K))
                          .norm();
    common_track_num++;
  }

  if (common_track_num < static_cast<int>(min_matches)) {
    spl::logging::Warning("CollectKeypoints", "Common track count too low");
    return false;
  }

  total_parallax /= std::max(common_track_num, 1);
  if (total_parallax < min_parallax) {
    spl::logging::Warning("CollectKeypoints", "Total parallax too low");
    return false;
  }

  return true;
}

}  // namespace rdvio