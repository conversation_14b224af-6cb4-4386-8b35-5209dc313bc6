#include <rdvio/geometry/stereo.h>
#include <rdvio/initialization/triangulation.h>

namespace rdvio {

TriangulationResult InitializerTriangulationProcess(const std::vector<matrix<3>>& Rs, const std::vector<vector<3>>& Ts,
                                                    const std::vector<vector<2>>& frame_i_keypoints,
                                                    const std::vector<vector<2>>& frame_j_keypoints,
                                                    const size_t initializer_min_triangulation) {
  std::vector<std::vector<vector<3>>> triangulation_points(Rs.size());
  std::vector<std::vector<char>> triangulation_status(Rs.size());
  std::vector<size_t> triangulation_counts(Rs.size());
  std::vector<double> triangulation_scores(Rs.size());

  size_t best_rt_index = 0;
  for (size_t i = 0; i < Rs.size(); ++i) {
    auto& points = triangulation_points[i];
    auto& status = triangulation_status[i];
    auto& count = triangulation_counts[i];
    auto& score = triangulation_scores[i];
    points.resize(frame_i_keypoints.size());
    status.resize(frame_i_keypoints.size());
    count = 0;
    score = 0;
    matrix<3, 4> P1, P2;
    P1.setIdentity();
    P2 << Rs[i], Ts[i];
    for (size_t j = 0; j < frame_i_keypoints.size(); ++j) {
      status[j] = 0;

      vector<4> q = triangulate_point(P1, P2, frame_i_keypoints[j].homogeneous(), frame_j_keypoints[j].homogeneous());
      vector<3> q1 = P1 * q;
      vector<3> q2 = P2 * q;

      // WARN(Jack): The variable name "in_front_of_camera_*" might be incorrect! Please convince yourselves this is
      // correct. This is my best interpretation, and if someone in future decides it is incorrect let's rename them :)
      const bool in_front_of_camera_1{q1[2] * q[3] > 0};
      const bool in_front_of_camera_2{q2[2] * q[3] > 0};
      if (in_front_of_camera_1 and in_front_of_camera_2) {
        // WARN(Jack): Similar to the comment above, my interpretation here might be incorrect! Please convince
        // yourself! Also note that depending on the input translation, this might be scaled or scale free, I am not
        // 100% sure. So the max_distance might be in meters, or it might be scale free.
        const double max_distance{1000};  // [m], or are we scale free here?
        const double distance_1{q1[2] / q[3]};
        const double distance_2{q2[2] / q[3]};
        if (distance_1 < max_distance and distance_2 < max_distance) {
          points[j] = q.hnormalized();
          status[j] = 1;
          count++;
          score += 0.5 * ((q1.hnormalized() - frame_i_keypoints[j]).squaredNorm() +
                          (q2.hnormalized() - frame_j_keypoints[j]).squaredNorm());
        }
      }
    }

    if (triangulation_counts[i] > initializer_min_triangulation &&
        triangulation_scores[i] < triangulation_scores[best_rt_index]) {
      best_rt_index = i;
    } else if (triangulation_counts[i] > triangulation_counts[best_rt_index]) {
      best_rt_index = i;
    }
  }

  return {best_rt_index, triangulation_points, triangulation_status, triangulation_counts};
}

}  // namespace rdvio