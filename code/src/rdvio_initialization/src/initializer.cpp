#include "rdvio/initialization/initializer.h"

#include <spl-logging/logging.hpp>

#include "rdvio/estimation/solver.h"
#include "rdvio/feature_tracker.h"
#include "rdvio/geometry/lie_algebra.h"
#include "rdvio/initialization/sfm_initialization.h"
#include "rdvio/map/frame.h"
#include "rdvio/map/map.h"
#include "rdvio/map/track.h"
#include "rdvio/sliding_window_tracker.h"
#include "rdvio/system_state_monitor.h"
#include "rdvio/types.h"

namespace rdvio {

Initializer::Initializer(const InitializerConfig& config, const std::shared_ptr<SystemStateMonitor> system_monitor)
    : config_(config) {
  component_monitor_ = system_monitor->RegisterComponent("Initializer");
  spl::logging::Debug("Initializer::Initializer", "Initializer constructed");
}

Initializer::~Initializer() = default;

void Initializer::MirrorKeyframeMap(const Map* const feature_tracking_map, size_t init_frame_id) {
  const size_t init_frame_index_last{feature_tracking_map->frame_index_by_id(init_frame_id)};
  const size_t init_frame_index_gap{config_.keyframe_gap};
  const size_t init_frame_index_distance{init_frame_index_gap * (config_.keyframe_num - 1)};

  init_frame_id = nil();

  if (init_frame_index_last < init_frame_index_distance) {
    map_.reset();
    return;
  }

  const size_t init_frame_index_first{init_frame_index_last - init_frame_index_distance};

  std::vector<size_t> init_keyframe_indices;
  for (size_t i = 0; i < config_.keyframe_num; ++i) {
    init_keyframe_indices.push_back(init_frame_index_first + i * init_frame_index_gap);
  }

  map_ = std::make_unique<Map>();
  for (size_t index : init_keyframe_indices) {
    map_->attach_frame(feature_tracking_map->get_frame(index)->clone());
  }

  for (size_t j = 1; j < map_->frame_num(); ++j) {
    Frame* old_frame_i{feature_tracking_map->get_frame(init_keyframe_indices[j - 1])};
    Frame* old_frame_j{feature_tracking_map->get_frame(init_keyframe_indices[j])};
    Frame* new_frame_i{map_->get_frame(j - 1)};
    Frame* new_frame_j{map_->get_frame(j)};
    for (size_t ki = 0; ki < old_frame_i->keypoint_num(); ++ki) {
      if (Track * track{old_frame_i->get_track(ki)}) {
        if (size_t kj{track->GetKeypointIndex(old_frame_j)}; kj != nil()) {
          new_frame_i->get_track(ki, nullptr)->AddKeypoint(new_frame_j, kj);
        }
      }
    }
    new_frame_j->preintegration.data.clear();
    for (size_t f = init_keyframe_indices[j - 1]; f < init_keyframe_indices[j]; ++f) {
      Frame* old_frame{feature_tracking_map->get_frame(f + 1)};
      std::vector<ImuData>& old_data{old_frame->preintegration.data};
      std::vector<ImuData>& new_data{new_frame_j->preintegration.data};
      new_data.insert(new_data.end(), old_data.begin(), old_data.end());
    }
  }
}

std::unique_ptr<Map> Initializer::Initialize() {
  component_monitor_->ReportState(ComponentState::INITIALIZING, "Checking if map initialized, solving SfM and IMU");
  spl::logging::Debug("Initializer::Initialize", "Checking if map initialized...");
  if (!map_) {
    return nullptr;
  }
  spl::logging::Debug("Initializer::Initialize", "Map initialized. Solving SfM...");
  if (!InitSfm()) {
    return nullptr;
  }
  spl::logging::Debug("Initializer::Initialize", "Solved SfM, solving IMU...");
  if (!InitImu()) {
    return nullptr;
  }
  spl::logging::Debug("Initializer::Initialize", "Solved IMU");

  component_monitor_->ReportState(ComponentState::INITIALIZING, "Solving Map.");
  map_->get_frame(0)->tag(FT_FIX_POSE) = true;

  const auto solver{Solver::Create()};
  for (size_t i = 0; i < map_->frame_num(); ++i) {
    solver->AddFrameStates(map_->get_frame(i));
  }
  std::unordered_set<Track*> visited_tracks;
  for (size_t i = 0; i < map_->frame_num(); ++i) {
    Frame* frame{map_->get_frame(i)};
    for (size_t j = 0; j < frame->keypoint_num(); ++j) {
      Track* track{frame->get_track(j)};
      if (!track) {
        continue;
      }
      if (!track->tag(TT_VALID)) {
        continue;
      }
      if (visited_tracks.count(track) > 0) {
        continue;
      }
      visited_tracks.insert(track);
      solver->AddTrackStates(track);
    }
  }
  for (size_t i = 0; i < map_->frame_num(); ++i) {
    Frame* frame{map_->get_frame(i)};
    for (size_t j = 0; j < frame->keypoint_num(); ++j) {
      Track* track{frame->get_track(j)};
      if (!track) {
        continue;
      }
      if (!track->all_tagged(TT_VALID, TT_TRIANGULATED)) {
        continue;
      }
      if (frame == track->FirstFrame()) {
        continue;
      }
      solver->AddFactor(frame->reprojection_error_factors[j].get());
    }
  }
  for (size_t j = 1; j < map_->frame_num(); ++j) {
    Frame* frame_i{map_->get_frame(j - 1)};
    Frame* frame_j{map_->get_frame(j)};
    if (frame_j->preintegration.integrate(frame_j->image->t, frame_i->motion.bias_gyroscope,
                                          frame_i->motion.bias_accelerometer, true, true)) {
      solver->PutFactor(Solver::CreatePreintegrationErrorFactor(frame_i, frame_j, frame_j->preintegration));
    }
  }
  solver->solve();

  for (size_t i = 0; i < map_->frame_num(); ++i) {
    map_->get_frame(i)->tag(FT_KEYFRAME) = true;
  }

  component_monitor_->ReportState(ComponentState::OK, "Successfully solved SfM, IMU and map.");

  return std::move(map_);
}

bool Initializer::InitSfm() { return InitializeSfm(config_.sfm_initializer_config, *map_); }

bool Initializer::InitImu() {
  ResetStates();
  SolveGyroBias();
  SolveGravityScaleVelocity();
  if (scale < 0.001 || scale > 1.0) {
    spl::logging::Warning("Initializer::InitImu", "Scale invalid");
    return false;
  }
  if (not config_.refine_imu) {
    spl::logging::Debug("Initializer::InitImu", "Imu refinement not requested, applying initialization");
    return ApplyInit();
  }
  RefineScaleVelocityViaGravity();
  if (scale < 0.001 || scale > 1.0) {
    spl::logging::Warning("Initializer::InitImu", "Scale invalid after refinement");
    return false;
  }
  return ApplyInit();
}

void Initializer::SolveGyroBias() {
  Preintegrate();
  matrix<3> A{matrix<3>::Zero()};
  vector<3> b{vector<3>::Zero()};

  for (size_t j = 1; j < map_->frame_num(); ++j) {
    const size_t i{j - 1};

    const Frame* frame_i{map_->get_frame(i)};
    const Frame* frame_j{map_->get_frame(j)};

    const PoseState pose_i{frame_i->get_pose(frame_i->imu)};
    const PoseState pose_j{frame_j->get_pose(frame_j->imu)};

    const quaternion& dq{frame_j->preintegration.delta.q};
    const matrix<3>& dq_dbg{frame_j->preintegration.jacobian.dq_dbg};
    A += dq_dbg.transpose() * dq_dbg;
    b += dq_dbg.transpose() * logmap((pose_i.q * dq).conjugate() * pose_j.q);
  }

  Eigen::JacobiSVD<matrix<3>> svd(A, Eigen::ComputeFullU | Eigen::ComputeFullV);
  bg = svd.solve(b);
}

void Initializer::SolveGravityScaleVelocity() {
  Preintegrate();
  int N = (int)map_->frame_num();
  matrix<> A;
  vector<> b;
  A.resize((N - 1) * 6, 3 + 1 + 3 * N);
  b.resize((N - 1) * 6);
  A.setZero();
  b.setZero();

  for (size_t j = 1; j < map_->frame_num(); ++j) {
    const size_t i = j - 1;

    const Frame* frame_i{map_->get_frame(i)};
    const Frame* frame_j{map_->get_frame(j)};
    const PreIntegrator::Delta& delta{frame_j->preintegration.delta};
    const PoseState camera_pose_i{frame_i->get_pose(frame_i->camera)};
    const PoseState camera_pose_j{frame_j->get_pose(frame_j->camera)};

    A.block<3, 3>(i * 6, 0) = -0.5 * delta.t * delta.t * matrix<3>::Identity();
    A.block<3, 1>(i * 6, 3) = camera_pose_j.p - camera_pose_i.p;
    A.block<3, 3>(i * 6, 4 + i * 3) = -delta.t * matrix<3>::Identity();
    b.segment<3>(i * 6) = frame_i->pose.q * delta.p + (frame_j->pose.q * frame_j->camera.P_body_sensor -
                                                       frame_i->pose.q * frame_i->camera.P_body_sensor);

    A.block<3, 3>(i * 6 + 3, 0) = -delta.t * matrix<3>::Identity();
    A.block<3, 3>(i * 6 + 3, 4 + i * 3) = -matrix<3>::Identity();
    A.block<3, 3>(i * 6 + 3, 4 + j * 3) = matrix<3>::Identity();
    b.segment<3>(i * 6 + 3) = frame_i->pose.q * delta.v;
  }

  const vector<> x{A.fullPivHouseholderQr().solve(b)};
  gravity = x.segment<3>(0).normalized() * kNominalGravity;
  scale = x(3);
  for (size_t i = 0; i < map_->frame_num(); ++i) {
    velocities[i] = x.segment<3>(4 + i * 3);
  }
}

void Initializer::RefineScaleVelocityViaGravity() {
  static const double damp{0.1};
  Preintegrate();
  const int N{static_cast<int>(map_->frame_num())};
  matrix<> A;
  vector<> b;
  vector<> x;
  A.resize((N - 1) * 6, 2 + 1 + 3 * N);
  b.resize((N - 1) * 6);
  x.resize(2 + 1 + 3 * N);

  for (size_t iter = 0; iter < 1; ++iter) {
    A.setZero();
    b.setZero();
    const matrix<3, 2> Tg{s2_tangential_basis(gravity)};

    for (size_t j = 1; j < map_->frame_num(); ++j) {
      const size_t i{j - 1};

      const Frame* frame_i{map_->get_frame(i)};
      const Frame* frame_j{map_->get_frame(j)};
      const PreIntegrator::Delta& delta{frame_j->preintegration.delta};
      const PoseState camera_pose_i{frame_i->get_pose(frame_i->camera)};
      const PoseState camera_pose_j{frame_j->get_pose(frame_j->camera)};

      A.block<3, 2>(i * 6, 0) = -0.5 * delta.t * delta.t * Tg;
      A.block<3, 1>(i * 6, 2) = camera_pose_j.p - camera_pose_i.p;
      A.block<3, 3>(i * 6, 3 + i * 3) = -delta.t * matrix<3>::Identity();
      b.segment<3>(i * 6) =
          0.5 * delta.t * delta.t * gravity + frame_i->pose.q * delta.p +
          (frame_j->pose.q * frame_j->camera.P_body_sensor - frame_i->pose.q * frame_i->camera.P_body_sensor);

      A.block<3, 2>(i * 6 + 3, 0) = -delta.t * Tg;
      A.block<3, 3>(i * 6 + 3, 3 + i * 3) = -matrix<3>::Identity();
      A.block<3, 3>(i * 6 + 3, 3 + j * 3) = matrix<3>::Identity();
      b.segment<3>(i * 6 + 3) = delta.t * gravity + frame_i->pose.q * delta.v;
    }

    x = A.fullPivHouseholderQr().solve(b);
    vector<2> dg = x.segment<2>(0);
    gravity = (gravity + damp * Tg * dg).normalized() * kNominalGravity;
  }

  scale = x(2);
  for (size_t i = 0; i < map_->frame_num(); ++i) {
    velocities[i] = x.segment<3>(3 + i * 3);
  }
}

void Initializer::ResetStates() {
  bg.setZero();
  ba.setZero();
  gravity.setZero();
  scale = 1;
  velocities.resize(map_->frame_num(), vector<3>::Zero());
}

void Initializer::Preintegrate() {
  for (size_t j = 1; j < map_->frame_num(); ++j) {
    Frame* frame_j{map_->get_frame(j)};
    frame_j->preintegration.integrate(frame_j->image->t, bg, ba, true, false);
  }
}

bool Initializer::ApplyInit(bool apply_ba, bool apply_velocity) {
  quaternion q = quaternion::FromTwoVectors(gravity, kNominalGravityVector);
  // This pose can transform the point from Cam optical frame to the gravity frame.
  T_gravity_cam_0.q = q;
  T_gravity_cam_0.p = vector<3>{0, 0, 0};

  for (size_t i = 0; i < map_->frame_num(); ++i) {
    Frame* frame{map_->get_frame(i)};
    PoseState imu_pose{frame->get_pose(frame->imu)};
    imu_pose.q = q * imu_pose.q;
    imu_pose.p = scale * (q * imu_pose.p);
    frame->set_pose(frame->imu, imu_pose);
    if (apply_velocity) {
      frame->motion.linear_velocity = q * velocities[i];
    } else {
      frame->motion.linear_velocity.setZero();
    }
    frame->motion.bias_gyroscope = bg;
    if (apply_ba) {
      frame->motion.bias_accelerometer = ba;
    } else {
      frame->motion.bias_accelerometer.setZero();
    }
  }
  size_t final_point_num = 0;
  for (size_t i = 0; i < map_->track_num(); ++i) {
    Track* track{map_->get_track(i)};
    if (auto p = track->Triangulate()) {
      track->SetLandmarkPoint(p.value());
      track->tag(TT_VALID) = true;
      track->tag(TT_TRIANGULATED) = true;
      final_point_num++;
    } else {
      track->tag(TT_VALID) = false;
    }
  }

  return final_point_num >= config_.min_landmarks;
}

Pose Initializer::GetInitialGravityToCameraPose() const { return T_gravity_cam_0; }

}  // namespace rdvio
