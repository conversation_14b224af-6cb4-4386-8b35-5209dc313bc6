#include <rdvio/geometry/essential.h>
#include <rdvio/geometry/homography.h>
#include <rdvio/geometry/stereo.h>
#include <rdvio/initialization/pose_generation.h>
#include <rdvio/types.h>
#include <spl-logging/logging.hpp>

namespace rdvio {

bool GeneratePoseHypothesis(const std::vector<vector<2>>& frame_i_keypoints,
                            const std::vector<vector<2>>& frame_j_keypoints, const double focal_length, const int seed,
                            std::vector<matrix<3>>& Rs, std::vector<vector<3>>& Ts) {
  const bool homography_successful{
      EstimatePosesWithHomography(frame_i_keypoints, frame_j_keypoints, focal_length, seed, Rs, Ts)};
  if (not homography_successful) {
    spl::logging::Warning("GeneratePoseHypothesis", "Homography based pose generation failed");
    return false;
  }

  EstimatePosesWithEssentialMatrix(frame_i_keypoints, frame_j_keypoints, focal_length, seed, Rs, Ts);

  return true;
}

bool EstimatePosesWithHomography(const std::vector<vector<2>>& frame_i_keypoints,
                                 const std::vector<vector<2>>& frame_j_keypoints, const double focal_length,
                                 const int seed, std::vector<matrix<3>>& Rs, std::vector<vector<3>>& Ts) {
  matrix<3> RH1, RH2;
  vector<3> TH1, TH2, nH1, nH2;

  matrix<3> H = find_homography_matrix(frame_i_keypoints, frame_j_keypoints, 0.7 / focal_length, 0.999, 1000, seed);
  if (!decompose_homography(H, RH1, RH2, TH1, TH2, nH1, nH2)) {
    spl::logging::Warning("EstimatePosesWithHomography", "Homography decomposition failed - indicates pure rotation");
    return false;  // is pure rotation
  }
  TH1 = TH1.normalized();
  TH2 = TH2.normalized();

  Rs.insert(Rs.end(), {RH1, RH1, RH2, RH2});
  Ts.insert(Ts.end(), {TH1, -TH1, TH2, -TH2});

  return true;
}

void EstimatePosesWithEssentialMatrix(const std::vector<vector<2>>& frame_i_keypoints,
                                      const std::vector<vector<2>>& frame_j_keypoints, const double focal_length,
                                      const int seed, std::vector<matrix<3>>& Rs, std::vector<vector<3>>& Ts) {
  matrix<3> RE1, RE2;
  vector<3> TE;

  matrix<3> E = find_essential_matrix(frame_i_keypoints, frame_j_keypoints, 0.7 / focal_length, 0.999, 1000, seed);
  decompose_essential(E, RE1, RE2, TE);
  TE = TE.normalized();

  Rs.insert(Rs.end(), {RE1, RE1, RE2, RE2});
  Ts.insert(Ts.end(), {TE, -TE, TE, -TE});
}

}  // namespace rdvio