#pragma once

#include <rdvio/map/frame.h>
#include <rdvio/types.h>

namespace rdvio {

bool CollectKeypoints(const Frame* const init_frame_i, const Frame* const init_frame_j, const size_t min_matches,
                      const double min_parallax, std::vector<vector<2>>& frame_i_keypoints,
                      std::vector<vector<2>>& frame_j_keypoints, std::vector<std::pair<size_t, size_t>>& init_matches);

}  // namespace rdvio