#pragma once

#include <rdvio/types.h>

namespace rdvio {

bool GeneratePoseHypothesis(const std::vector<vector<2>>& frame_i_keypoints,
                            const std::vector<vector<2>>& frame_j_keypoints, const double focal_length, const int seed,
                            std::vector<matrix<3>>& Rs, std::vector<vector<3>>& Ts);

bool EstimatePosesWithHomography(const std::vector<vector<2>>& frame_i_keypoints,
                                 const std::vector<vector<2>>& frame_j_keypoints, const double focal_length,
                                 const int seed, std::vector<matrix<3>>& Rs, std::vector<vector<3>>& Ts);

void EstimatePosesWithEssentialMatrix(const std::vector<vector<2>>& frame_i_keypoints,
                                      const std::vector<vector<2>>& frame_j_keypoints, const double focal_length,
                                      const int seed, std::vector<matrix<3>>& Rs, std::vector<vector<3>>& Ts);

}  // namespace rdvio