#pragma once

#include <rdvio/types.h>

namespace rdvio {

using TriangulationResult =
    std::tuple<size_t, std::vector<std::vector<vector<3>>>, std::vector<std::vector<char>>, std::vector<size_t>>;

TriangulationResult InitializerTriangulationProcess(const std::vector<matrix<3>>& Rs, const std::vector<vector<3>>& Ts,
                                                    const std::vector<vector<2>>& frame_i_keypoints,
                                                    const std::vector<vector<2>>& frame_j_keypoints,
                                                    const size_t initializer_min_triangulation);

}  // namespace rdvio