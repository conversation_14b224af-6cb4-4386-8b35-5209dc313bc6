#pragma once

#include "rdvio/config.hpp"
#include "rdvio/system_state_monitor.h"
#include "rdvio/types.h"

namespace rdvio {

class Map;

class Initializer {
 public:
  Initializer(const InitializerConfig& config, const std::shared_ptr<SystemStateMonitor> system_monitor);
  ~Initializer();

  void MirrorKeyframeMap(const Map* const feature_tracking_map, size_t init_frame_id);

  std::unique_ptr<Map> Initialize();

  Pose GetInitialGravityToCameraPose() const;

 private:
  const InitializerConfig config_;
  std::unique_ptr<Map> map_;

  bool InitSfm();

  bool InitImu();

  void SolveGyroBias();
  void SolveGravityScaleVelocity();
  void RefineScaleVelocityViaGravity();

  void ResetStates();
  void Preintegrate();
  bool ApplyInit(bool apply_ba = false, bool apply_velocity = true);

  vector<3> bg;
  vector<3> ba;
  vector<3> gravity;
  double scale;
  // This pose can transform a point from the Cam optical frame to the gravity aligned frame during initialization.
  Pose T_gravity_cam_0;
  std::vector<vector<3>> velocities;

  std::shared_ptr<ComponentHandle> component_monitor_;
};

}  // namespace rdvio
