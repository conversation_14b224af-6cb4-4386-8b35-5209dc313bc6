#include <gtest/gtest.h>

#include <rdvio/initialization/pose_generation.h>
#include <rdvio/testing_util/unit/construct_map_fixture.hpp>

using namespace rdvio;

class PoseGenerationFixture : public ConstructMapFixture {
 protected:
  PoseGenerationFixture() {
    const Frame* const frame_0{map_->get_frame(0)};
    const Frame* const frame_1{map_->get_frame(2)};

    for (size_t keypoint_idx{0}; keypoint_idx < frame_0->keypoint_num(); ++keypoint_idx) {
      frame_0_keypoints_2d_.push_back(frame_0->GetBearing(keypoint_idx).hnormalized());
      frame_1_keypoints_2d_.push_back(frame_1->GetBearing(keypoint_idx).hnormalized());
    }
  }

  std::vector<vector<2>> frame_0_keypoints_2d_;
  std::vector<vector<2>> frame_1_keypoints_2d_;
  // cppcheck-suppress unusedStructMember
  const double focal_length_{100};
  // cppcheck-suppress unusedStructMember
  const int seed_{648};
};

TEST_F(PoseGenerationFixture, xxx) {
  std::vector<matrix<3>> Rs;
  std::vector<vector<3>> Ts;
  const bool hypothesis_generation_successful{
      GeneratePoseHypothesis(frame_0_keypoints_2d_, frame_1_keypoints_2d_, focal_length_, seed_, Rs, Ts)};

  ASSERT_TRUE(hypothesis_generation_successful);

  // We just test one from each method, and not all eight hypothesis :)
  // Homography
  const matrix<3> homography_R{Rs[0]};
  const vector<3> homography_T{Ts[0]};
  EXPECT_TRUE(homography_R.diagonal().isApprox(vector<3>{1, 1, 1}));  // identity rotation
  EXPECT_TRUE(homography_T.isApprox(vector<3>{0, 0, 1}, 1e-3));       // forward motion (?)

  // Essential matrix - different values on cuda agent vs l4t for the essential matrix
  // WARN(Jack): This part of the test is super hacky! Basically the problem is, that the essential matrix optimization
  // fails differently between cuda and l4t, and on l4t it fails in two different ways! Therefore here we need three
  // different values :( If this part of the test gets any more complicated, please just remove it! We are testing a
  // failed optimization anyway, not worth it :)
  const matrix<3> essential_matrix_R{Rs[4]};
  const vector<3> essential_matrix_T{Ts[4]};

  const bool cuda_agent_R{essential_matrix_R.diagonal().isApprox(vector<3>{0.742384, 0.704083, 0.45048}, 1e-3)};
  const bool l4t_36_agent_R_1{essential_matrix_R.diagonal().isApprox(vector<3>{0.72923, 0.73378, 0.463058}, 1e-2)};
  const bool l4t_36_agent_R_2{essential_matrix_R.diagonal().isApprox(vector<3>{-0.412339, 0.0158366, -0.603237}, 1e-2)};
  EXPECT_TRUE(cuda_agent_R or l4t_36_agent_R_1 or l4t_36_agent_R_2);

  const bool cuda_agent_T{essential_matrix_T.isApprox(vector<3>{0.681803, 0.726124, -0.0888215}, 1e-3)};
  const bool l4t_36_agent_T_1{essential_matrix_T.isApprox(vector<3>{-0.704302, -0.699081, 0.123469}, 1e-2)};
  const bool l4t_36_agent_T_2{essential_matrix_T.isApprox(vector<3>{-0.648, -0.761513, 0.0139216}, 1e-2)};
  EXPECT_TRUE(cuda_agent_T or l4t_36_agent_T_1 or l4t_36_agent_T_2);
}
