#include <gtest/gtest.h>

#include <rdvio/config.hpp>
#include <rdvio/initialization/sfm_initialization.h>
#include <rdvio/testing_util/unit/construct_map_fixture.hpp>
#include <rdvio/types.h>

using namespace rdvio;

TEST_F(ConstructMapFixture, xxx) {
  const std::string calib_file{
      "/testroot/code/test/test_data/5point_straight_trajectory/sensors_5point_straight_trajectory.yaml"};
  const std::string config_file{
      "/testroot/code/test/test_data/5point_straight_trajectory/algorithm_5point_straight_trajectory.yaml"};
  std::shared_ptr<Config> config{std::make_shared<Config>(calib_file, config_file)};

  const auto initializer_config{config->GetInitializerConfig()};
  const bool sfm_initialization_success{InitializeSfm(initializer_config.sfm_initializer_config, *map_)};
  ASSERT_TRUE(sfm_initialization_success);

  const auto frame_10m_pose{map_->get_frame(0)->pose};
  EXPECT_TRUE(frame_10m_pose.p.isApprox(vector<3>{0, 0, 0}));
  EXPECT_TRUE(frame_10m_pose.q.isApprox(quaternion{1, 0, 0, 0}));

  // I would really like the frame_8m pose to be at {0,0,0.5} and {1,0,0,0}, but for some reason it does not optimize!
  // Maybe the initialization is too far away (i.e. the frame rate is too low in my simulated data), for it to work. I
  // have to leave this to a future engineer to figure this out!s

  const auto frame_6m_pose{map_->get_frame(2)->pose};
  EXPECT_TRUE(frame_6m_pose.p.isApprox(vector<3>{0, 0, 1}, 1e-3));
  EXPECT_TRUE(frame_6m_pose.q.isApprox(quaternion{1, 0, 0, 0}, 1e-3));

  // We do not have scale information here, so we just check that the ratio and sign of x or y to the z coordinate is
  // correct.
  const auto track_1_landmark{map_->get_track(1)->GetLandmarkPoint()};
  EXPECT_FLOAT_EQ((track_1_landmark(0) / track_1_landmark(2)), 0.1);
  EXPECT_FLOAT_EQ((track_1_landmark(1) / track_1_landmark(2)), 0.1);

  const auto track_4_landmark{map_->get_track(4)->GetLandmarkPoint()};
  EXPECT_FLOAT_EQ((track_4_landmark(0) / track_4_landmark(2)), -0.1);
  EXPECT_FLOAT_EQ((track_4_landmark(1) / track_4_landmark(2)), 0.1);
}
