#include <gtest/gtest.h>

#include <rdvio/initialization/triangulation.h>

using namespace rdvio;

class InitializerTriangulationFixture : public ::testing::Test {
 protected:
  // cppcheck-suppress [unusedFunction, unmatchedSuppression]
  void SetUp() {
    const matrix<3> R_false{{0, 1, 0}, {0, 0, 1}, {1, 0, 0}};  // some random rotation matrix
    const matrix<3> R_true{{1, 0, 0}, {0, 1, 0}, {0, 0, 1}};   // motion is straight forward
    Rs_.push_back(R_false);
    Rs_.push_back(R_true);

    const vector<3> t_false{10, 10, 12};  // some random translation
    const vector<3> t_true{0, 0, -2};
    Ts_.push_back(t_false);
    Ts_.push_back(t_true);

    // WARN(Jack): Something that cost me a lot of time is what "keypoints" actually means! Apparently the keypoint are
    // actually stored as a bearing vector and not as pixels as I had first assumed! The following processing is done
    // to produce the input data exactly as it is in Initializer::InitSfm().
    // NOTE(Jack): we add a random point ({3,3,20}) as the sixth element just to make sure that it rejects it properly
    std::vector<vector<3>> frame_i_landmarks{{0, 0, 10},  {1, 1, 10},  {-1, -1, 10},
                                             {1, -1, 10}, {-1, 1, 10}, {3, 3, 20}};
    std::vector<vector<3>> frame_j_landmarks{{0, 0, 8}, {1, 1, 8}, {-1, -1, 8}, {1, -1, 8}, {-1, 1, 8}, {3, 3, 20}};
    for (size_t i{0}; i < std::size(frame_i_landmarks); ++i) {
      // Convert to "bearing"
      frame_i_landmarks[i] = frame_i_landmarks[i] / frame_i_landmarks[i].norm();
      frame_j_landmarks[i] = frame_j_landmarks[i] / frame_j_landmarks[i].norm();

      // Normalize them exactly like they are in the initializer.cpp
      frame_i_keypoints_.push_back(frame_i_landmarks[i].hnormalized());
      frame_j_keypoints_.push_back(frame_j_landmarks[i].hnormalized());
    }
  }

  std::vector<matrix<3>> Rs_;
  std::vector<vector<3>> Ts_;
  std::vector<vector<2>> frame_i_keypoints_;
  std::vector<vector<2>> frame_j_keypoints_;
  // cppcheck-suppress unusedStructMember
  const size_t initializer_min_triangulation_{1};  // TODO(Jack): set value to 4!
};

TEST_F(InitializerTriangulationFixture, xxx) {
  const auto [best_rt_index, triangulation_points, triangulation_status,
              triangulation_counts]{InitializerTriangulationProcess(Rs_, Ts_, frame_i_keypoints_, frame_j_keypoints_,
                                                                    initializer_min_triangulation_)};

  // Out of the two possible transformation in the test fixture, "_false" and "_true", it should identify the true one
  // (ID #1) as the only plausible one.
  EXPECT_EQ(best_rt_index, 1);
  EXPECT_EQ(triangulation_counts[0], 0);
  EXPECT_EQ(triangulation_counts[1], 4);

  // No successful triangulations using the "_false" transformation.
  for (const auto& status : triangulation_status[0]) {
    EXPECT_EQ(status, '\0');
  }

  // Four out of six successful triangulations using the "_true" transformation.
  EXPECT_EQ(triangulation_status[1][0], '\0');
  for (size_t i{1}; i <= 4; ++i) {
    EXPECT_EQ(triangulation_status[1][i], '\1');
  }
  EXPECT_EQ(triangulation_status[1][5], '\0');
}

class HighAltitudeInitializerTriangulationFixture : public ::testing::Test {
 protected:
  // cppcheck-suppress [unusedFunction, unmatchedSuppression]
  void SetUp() {
    const matrix<3> R_false{{0, 1, 0}, {0, 0, 1}, {1, 0, 0}};  // some random rotation matrix
    const matrix<3> R_true{{1, 0, 0}, {0, 1, 0}, {0, 0, 1}};   // camera has the same orientation in both frames
    Rs_.push_back(R_false);
    Rs_.push_back(R_true);

    const vector<3> t_false{10, 10, 12};  // some random translation
    const double motion_x{10};
    const vector<3> t_true{motion_x, 0, 0};  // move sideways - we are in the camera coordinate frame (z-axis forward)
    Ts_.push_back(t_false);
    Ts_.push_back(t_true);

    const double offset{5};
    const double elevation{600};
    std::vector<vector<3>> frame_i_landmarks{{0, 0, elevation},
                                             {offset, offset, elevation},
                                             {-offset, -offset, elevation},
                                             {offset, -offset, elevation},
                                             {-offset, offset, elevation},
                                             {3, 3, 20}};  // random outlier point
    std::vector<vector<3>> frame_j_landmarks{{0 + motion_x, 0, elevation},
                                             {offset + motion_x, offset, elevation},
                                             {-offset + motion_x, -offset, elevation},
                                             {offset + motion_x, -offset, elevation},
                                             {-offset + motion_x, offset, elevation},
                                             {3, 3, 20}};
    for (size_t i{0}; i < std::size(frame_i_landmarks); ++i) {
      // Convert to "bearing"
      frame_i_landmarks[i] = frame_i_landmarks[i] / frame_i_landmarks[i].norm();
      frame_j_landmarks[i] = frame_j_landmarks[i] / frame_j_landmarks[i].norm();

      // Normalize them exactly like they are in the initializer.cpp
      frame_i_keypoints_.push_back(frame_i_landmarks[i].hnormalized());
      frame_j_keypoints_.push_back(frame_j_landmarks[i].hnormalized());
    }
  }

  std::vector<matrix<3>> Rs_;
  std::vector<vector<3>> Ts_;
  std::vector<vector<2>> frame_i_keypoints_;
  std::vector<vector<2>> frame_j_keypoints_;
  // cppcheck-suppress unusedStructMember
  const size_t initializer_min_triangulation_{4};
};

TEST_F(HighAltitudeInitializerTriangulationFixture, xxx) {
  const auto [best_rt_index, triangulation_points, triangulation_status,
              triangulation_counts]{InitializerTriangulationProcess(Rs_, Ts_, frame_i_keypoints_, frame_j_keypoints_,
                                                                    initializer_min_triangulation_)};

  EXPECT_EQ(best_rt_index, 1);
  EXPECT_EQ(triangulation_counts[0], 0);
  EXPECT_EQ(triangulation_counts[1], 5);

  // No successful triangulations using the "_false" transformation.
  for (const auto& status : triangulation_status[0]) {
    EXPECT_EQ(status, '\0');
  }

  // Successful triangulations for sidewards motion except for the outlier point
  for (size_t i{0}; i <= 4; ++i) {
    EXPECT_EQ(triangulation_status[1][i], '\1');
  }
  EXPECT_EQ(triangulation_status[1][5], '\0');
}