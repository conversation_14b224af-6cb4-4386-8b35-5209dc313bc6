#include <gtest/gtest.h>

#include <rdvio/initialization/collect_keypoints.h>
#include <rdvio/testing_util/unit/construct_map_fixture.hpp>

using namespace rdvio;

TEST_F(ConstructMapFixture, TestCollectKeypoints) {
  std::vector<vector<2>> frame_i_keypoints;
  std::vector<vector<2>> frame_j_keypoints;
  std::vector<std::pair<size_t, size_t>> init_matches;
  const bool good_keypoints{CollectKeypoints(map_->get_frame(0), map_->get_frame(1), 4, 1, frame_i_keypoints,
                                             frame_j_keypoints, init_matches)};

  EXPECT_TRUE(good_keypoints);
  EXPECT_EQ(std::size(frame_i_keypoints), 5);
  EXPECT_EQ(std::size(frame_j_keypoints), 5);
  EXPECT_EQ(std::size(init_matches), 5);
}
