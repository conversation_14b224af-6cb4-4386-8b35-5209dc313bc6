#include <gtest/gtest.h>

#include <rdvio/map/frame.h>
#include <rdvio/map/map.h>
#include <rdvio/map/track.h>

namespace rdvio {

// NOTE(Jack): This test fixture should ideally be used for all testing in the library. But in some cases you will need
// more simple versions or more complex versions. For more complex versions you can inherit from this as done by the
// PoseGenerationFixture fixture.
//
// The basic scenario described here is a camera which moves forward facing a wall 10m away with five landmarks on it.
// The camera approaches this wall in two steps, for a total of three frames. As we are in the camera frame, the forward
// motion happens along the z-axis and not the x-axis like you might expect. The details of each frame in the map can
// be found directly in the MakeFrame method.
class ConstructMapFixture : public ::testing::Test {
 protected:
  ConstructMapFixture() {
    auto frame_10m{MakeFrame(10)};
    auto frame_8m{MakeFrame(8)};
    auto frame_6m{MakeFrame(6)};

    for (size_t keypoint_index{0}; keypoint_index < frame_10m->keypoint_num(); ++keypoint_index) {
      Track* const track{frame_10m->get_track(keypoint_index, map_.get())};
      track->AddKeypoint(frame_8m.get(), keypoint_index);
      track->AddKeypoint(frame_6m.get(), keypoint_index);
      // All landmarks in the reference frame (frame_10m) are initialized with z = 10,
      // so the inverse depth for all keypoints is 1/10.
      track->landmark.inv_depth = 1.0 / 10.0;
    }

    map_->attach_frame(std::move(frame_10m), 0);
    map_->attach_frame(std::move(frame_8m), 1);
    map_->attach_frame(std::move(frame_6m), 2);
  }

  std::unique_ptr<Map> map_{std::make_unique<Map>()};

 private:
  static std::vector<vector<3>> CalculateBearings(const std::vector<vector<3>>& landmarks) {
    std::vector<vector<3>> bearings;
    bearings.resize(std::size(landmarks));
    for (size_t i{0}; i < std::size(bearings); ++i) {
      bearings[i] = landmarks[i] / landmarks[i].norm();
    }
    return bearings;
  }

  static std::unique_ptr<Frame> MakeFrame(const float z_distance) {
    auto frame{std::make_unique<Frame>()};

    frame->pose.p = {0, 0, (10 - z_distance)};
    frame->pose.q = {1, 0, 0, 0};

    const matrix<3, 3> camera_intrinsics{{100, 0, 75}, {0, 100, 50}, {0, 0, 1}};
    frame->K = camera_intrinsics;
    const ExtrinsicParams sensor_extrinsics{{1, 0, 0, 0}, {0, 0, 0}};
    frame->camera = sensor_extrinsics;
    frame->imu = sensor_extrinsics;
    MotionState motion_state;
    motion_state.linear_velocity = {0.0, 0.0, 0.5};
    motion_state.bias_gyroscope = {0, 0, 0};
    motion_state.bias_accelerometer = {0, 0, 0};
    frame->motion = motion_state;

    const std::vector<vector<3>> landmarks{
        {0, 0, z_distance}, {1, 1, z_distance}, {-1, -1, z_distance}, {1, -1, z_distance}, {-1, 1, z_distance}};
    const auto bearings{CalculateBearings(landmarks)};
    for (size_t keypoint_index{0}; keypoint_index < std::size(bearings); ++keypoint_index) {
      frame->append_keypoint(Feature(bearings[keypoint_index]));
    }

    return frame;
  }
};

}  // namespace rdvio