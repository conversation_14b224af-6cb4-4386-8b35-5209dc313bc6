#include <gtest/gtest.h>

#include "rdvio/extra/camera.h"
#include "rdvio/extra/opencv_image.h"
#include "rdvio/rdvio.hpp"

namespace rdvio {

class ImageFrontendFixture : public ::testing::Test {
 protected:
  // cppcheck-suppress [unusedFunction, unmatchedSuppression]
  ImageFrontendFixture() {
    const std::string filename1{"/testroot/code/test/test_data/feature_tracker/1403715282262142976.png"};
    const std::string filename2{"/testroot/code/test/test_data/feature_tracker/1403715282312143104.png"};

    const std::string sensor_config_path{"/testroot/code/test/test_data/euroc/euroc.yaml"};
    const std::string slam_config_path{"/testroot/code/test/test_data/euroc/setting.yaml"};

    config_ = std::make_shared<Config>(sensor_config_path, slam_config_path);
    feature_tracker_config_ = config_->GetFeatureTrackerConfig();
    const auto camera{std::make_shared<extra::Camera>(config_->GetCameraConfig())};

    opencv_image1_ = ConstructOpenCvImage(filename1, 0, camera);
    opencv_image2_ = ConstructOpenCvImage(filename2, 1, camera);
  }

  std::shared_ptr<extra::OpenCvImage> ConstructOpenCvImage(const std::string& filename, const double timestamp,
                                                           const std::shared_ptr<extra::Camera> camera) {
    const cv::Mat img_distorted = cv::imread(filename, cv::IMREAD_GRAYSCALE);
    const cv::Mat dist_coeffs = (cv::Mat_<float>(4, 1) << -0.28340811, 0.07395907, 0.00019359, 1.76187114e-05);
    const cv::Mat K = (cv::Mat_<float>(3, 3) << 458.654, 0, 367.215, 0, 457.296, 248.375, 0, 0, 1);

    cv::Mat img;
    cv::undistort(img_distorted, img, K, dist_coeffs);

    const auto opencv_image{std::make_shared<extra::OpenCvImage>(img, timestamp, camera,
                                                                 feature_tracker_config_.optical_flow_config, false)};

    opencv_image->PreProcess(feature_tracker_config_.clahe_config);

    return opencv_image;
  }

  std::shared_ptr<Config> config_;
  FeatureTrackerConfig feature_tracker_config_;
  std::shared_ptr<extra::OpenCvImage> opencv_image1_;
  std::shared_ptr<extra::OpenCvImage> opencv_image2_;
};

}  // namespace rdvio