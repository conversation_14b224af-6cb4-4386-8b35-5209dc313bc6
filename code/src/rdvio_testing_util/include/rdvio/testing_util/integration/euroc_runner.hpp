#pragma once

#include <rdvio/rdvio.hpp>
#include <rdvio/testing_util/integration/euroc_dataset.hpp>
#include <rdvio/util/trajectory_logger.hpp>

namespace rdvio {

RigidBodyState EurocRunner(const std::string& calib_file, const std::string& config_file,
                           const std::string& data_directory, const std::string& log_file,
                           const std::string& odometry_log_file, const double start_offset_sec = 0.0,
                           const double duration_sec = 600.0) {
  auto euroc{dataset::EuRoC(data_directory, true, false)};
  dataset::set_logger(false);

  auto vio{rdvio::Odometry(calib_file, config_file)};
  const auto trajectory_logger{std::make_unique<rdvio::TrajectoryLogger>(log_file, TrajectoryType::TUM)};
  const auto odometry_logger{
      std::make_unique<rdvio::TrajectoryLogger>(odometry_log_file, TrajectoryType::ODOMETRY, ",")};

  std::shared_ptr<dataset::dataclip_t> data;
  double initial_timestamp_sec{-1.0};
  while (data = euroc.next()) {
    if (initial_timestamp_sec < 0) {
      initial_timestamp_sec = data->timestamp.sec();
    }

    const double start_timestamp_sec{initial_timestamp_sec + start_offset_sec};
    if (data->timestamp.sec() < start_timestamp_sec) {
      continue;
    }

    const double end_timestamp_sec{start_timestamp_sec + duration_sec};
    if (data->timestamp.sec() > end_timestamp_sec) {
      break;
    }

    if (data->has_motion()) {
      vio.AddMotion(data->timestamp.sec(), data->acc(), data->gyro());
    }

    if (data->has_cam0()) {
      vio.AddFrame(data->timestamp.sec(), data->cam0);

      if (vio.State() == rdvio::SystemState::TRACKING) {
        trajectory_logger->Log(vio.TransformWorldCam(), data->timestamp.sec());
        odometry_logger->Log(vio.TransformOdomImu(), data->timestamp.sec(), vio.GetLinearBodyVelocity(),
                             vio.GetAngularBodyVelocity());
      }

#ifdef USE_MULTI_THREADING
      std::this_thread::sleep_for(std::chrono::milliseconds(300));
#endif
    }
  }

  const RigidBodyState vio_estimate{vio.GetRigidBodyState(ReferenceFrame::BODY)};

  trajectory_logger->Destroy();
  odometry_logger->Destroy();

  vio.Exit();

  return vio_estimate;
}

}  // namespace rdvio