#pragma once

#include <filesystem>
#include <fstream>
#include <memory>
#include <unordered_map>
#include <vector>

#include <Eigen/Core>
#include <opencv2/opencv.hpp>
#include <yaml-cpp/yaml.h>

namespace dataset {

namespace fs = std::filesystem;

static bool __log_d__ = false;
/// @brief set logger flag
void set_logger(const bool log) { __log_d__ = log; }

/**
 * @brief Timestamp structure
 * @note [Convert] use sec(), ms(), us(), ns() to get converted data
 * @note [Template] call with template to get different data type
 */
struct timestamp_t {
  // basic nano second
  const uint64_t ns;

  /// @brief default constructor
  explicit timestamp_t(uint64_t t) : ns(t) {}

  /// @brief convert to second
  template <typename T = double>
  const T sec() const {
    return static_cast<T>(__div__(1e9));
  }

  /// @brief inline division
  inline double __div__(const double& d) const { return static_cast<double>(ns) / d; }
};

/**
 * @brief Motion data structure (accelerometer or gyroscope)
 * @note [Format] data stored in x, y, z format.
 * @note [Convert] data can be converted to Eigen format using the eigen() function.
 */
struct motion_t {
  double x, y, z;

  /// @brief convert to Eigen
  template <typename T = double>
  Eigen::Vector3<T> eigen() const {
    return Eigen::Vector3<T>(x, y, z);
  }
};

/**
 * @brief instant clip for data
 * @param ts timestamp
 * @param cam0 main frame data
 * @param cam1 extra frame data
 * @param depth depth frame data
 * @param accel imu acceleration
 * @param gyro imu gyroscope
 * @note The frame images are stored in OpenCV Mat format.
 */
struct dataclip_t {
  /// @brief default constructor
  explicit dataclip_t(const uint64_t ns) : timestamp(ns) {}
  /// @brief default destructor
  ~dataclip_t() {
    if (!cam0.empty()) {
      cam0.release();
    }
    if (!cam1.empty()) {
      cam1.release();
    }
    if (!depth.empty()) {
      depth.release();
    }
  }
  // timestamp
  const timestamp_t timestamp;
  // main frame data
  cv::Mat cam0;
  // extra frame data
  cv::Mat cam1, depth;
  // imu accelerometer and gyroscope
  motion_t accelerometer{};
  motion_t gyroscope{};
  // flags
  enum { CAM0 = 0, CAM1 = 1, DEPTH = 2, ACC = 3, GYRO = 4 };
  bool available[5] = {false, false, false, false, false};
  /**
   * @brief set and unset data availability
   */
  void set(const int idx) { available[idx] = true; }
  bool has_cam0() { return available[CAM0]; }
  bool has_motion() { return available[ACC] && available[GYRO]; }
  template <typename T = double>
  Eigen::Vector3<T> acc() {
    return accelerometer.eigen<T>();
  }
  template <typename T = double>
  Eigen::Vector3<T> gyro() {
    return gyroscope.eigen<T>();
  }
};

/**
 * @brief Intrinsic camera parameters
 * @note [Format] fx, fy, cx, cy
 * @note [Convert] data can be converted to Eigen format using the eigen() function.
 * @note [Convert] data can be converted to cv::Mat format using the cv() function.
 */
struct intrinsic_t {
  // camera intrinsict
  float fx, fy, cx, cy;
  /// @brief convert to Eigen
  template <typename T = float>
  Eigen::Matrix<T, 3, 3> eigen() const {
    Eigen::Matrix<T, 3, 3> K;
    K << fx, 0, cx, 0, fy, cy, 0, 0, 1;
    return K;
  }
  /// @brief convert to cv::Mat
  template <typename T = float>
  cv::Mat cv() const {
    return (cv::Mat_<T>(3, 3) << fx, 0, cx, 0, fy, cy, 0, 0, 1);
  }
};

/**
 * @brief Distortion parameters
 * @note [Format] k1, k2, p1, p2, k3
 * @note [Convert] data can be converted to Eigen format using the eigen() function.
 * @note [Convert] data can be converted to cv::Mat format using the cv() function.
 */
struct distortion_t {
  // distortion parameters
  double k1 = 0, k2 = 0, p1 = 0, p2 = 0, k3 = 0;
  /// @brief convert to Eigen
  template <typename T = double>
  Eigen::VectorX<T> eigen(bool k3 = false) const {
    if (k3) {
      Eigen::Matrix<T, 5, 1> D;
      D << k1, k2, p1, p2, k3;
      return D;
    }
    Eigen::Matrix<T, 4, 1> D;
    D << k1, k2, p1, p2;
    return D;
  }
  /// @brief convert to cv::Mat
  template <typename T = double>
  cv::Mat cv(bool k3 = false) const {
    if (k3) {
      return (cv::Mat_<T>(5, 1) << k1, k2, p1, p2, k3);
    }
    return (cv::Mat_<T>(4, 1) << k1, k2, p1, p2);
  }
};

/**
 * @brief Load vector from yaml
 */
template <typename T = float>
static Eigen::VectorX<T> vector_from_yaml(const YAML::Node& node) {
  const std::vector<T> raw_data = node.as<std::vector<T>>();
  const int rows = raw_data.size();
  // convert to eigen
  Eigen::VectorX<T> V = Eigen::Map<const Eigen::VectorX<T>>(raw_data.data(), rows);
  return V;
}

/**
 * @brief Simple csv reader
 */
class csvReader {
 public:
  /**
   * @brief Constructor
   * @param filepath csv file path
   * @note [Structure] data must be separated by comma ',' and comment with '#'
   */
  explicit csvReader(const std::string& filepath) : ifs_(filepath) {
    if (!ifs_.is_open()) {
      throw std::runtime_error("cannot open file: " + filepath);
    }
    ss_cache_ = std::stringstream("");
  }

  /// @brief whether end of last data
  bool isEnd() { return ss_end() && (ifs_.peek() == EOF || ifs_.eof()); }

  /**
   * @brief get next data
   * @param n number of data to read, 1 by default
   * @note [Return] return vector of data
   */
  template <typename T>
  std::vector<T> next(size_t n = 1) {
    std::vector<T> data;
    for (size_t i = 0; i < n; i++) {
      if (isEnd()) {
        break;
      }
      T d = _read_next<T>();
      data.push_back(d);
    }
    return data;
  }

  /**
   * @brief ignore next data
   * @param n number of data to ignore, 1 by default
   */
  void ignore(size_t n = 1) {
    for (size_t i = 0; i < n; i++) {
      if (isEnd()) {
        break;
      }
      _read_next<std::string>();
    }
  }

  /// @brief close file
  void close() { ifs_.close(); }

 private:
  /// @brief read next single data
  template <typename T>
  T _read_next() {
    if (ss_end()) {
      bool available = false;
      std::string line = "";
      while (!ifs_.eof()) {
        std::getline(ifs_, line);
        available = format_and_check(line);
        if (available) {
          break;
        }
      }
      if (!available) {
        return T();
      } else {
        ss_cache_ = std::stringstream(line);
      }
    }

    T data;
    ss_cache_ >> data;
    if (ss_cache_.fail()) {
      throw std::runtime_error("failed to read data of type: " + std::string(typeid(data).name()) +
                               " from line: " + ss_cache_.str());
    }
    return data;
  }

  /// @brief format ane check line
  bool format_and_check(std::string& line) {
    if (line.size() == 0) {
      return false;
    }
    std::replace(line.begin(), line.end(), ',', ' ');
    std::replace(line.begin(), line.end(), '\t', ' ');
    size_t first = line.find_first_not_of(' '), last = line.find_last_not_of(' ');
    line = line.substr(first, last - first + 1);
    return (!line.empty() && line.front() != '#');
  }

  bool ss_end() {
    if (ss_cache_.eof() || ss_cache_.str().empty()) {
      return true;
    }
    char c = ss_cache_.peek();
    return c == '\0' || c == '\n' || c == '\r' || c == EOF;
  }

  std::ifstream ifs_;
  std::stringstream ss_cache_;

};  // class CSV

/**
 * @brief Base class
 * @note The base class is used to define the basic structure of the dataset.
 */
class Base {
 public:
  /**
   * @brief Constructor
   * @param base_dir base path or directory of the dataset
   */
  Base(const std::string& base_dir) : base_dir_(fs::canonical(fs::absolute(base_dir))) {
    if (!fs::exists(base_dir_)) {
      throw std::runtime_error("file or directory not found: " + base_dir_.string());
    }
  }

  /// @brief Destructor
  virtual ~Base() {}

  /// @brief get next data
  virtual std::shared_ptr<dataclip_t> next() = 0;

  /// @brief get data by timestamp
  virtual std::shared_ptr<dataclip_t> at(const uint64_t ns) { throw std::runtime_error("Not implemented: at(time)"); };

  /// @brief reset data
  virtual void reset() { throw std::runtime_error("Not implemented: reset()"); };

 protected:
  /// @brief get and create data by index
  std::shared_ptr<dataclip_t> get_(const uint64_t ns, const bool create_if_empty = false) {
    if (data_map_.count(ns) > 0) {
      return data_map_[ns];
    } else if (create_if_empty) {
      const auto it = std::make_shared<dataclip_t>(ns);
      data_.push_back(it);
      data_map_[ns] = it;
      return data_map_[ns];
    }
    return nullptr;
  }

  /// @brief free last
  virtual void _free_last() {
    if (data_pt_ == 0) {
      return;
    }
    const auto ts = data_[data_pt_ - 1]->timestamp.ns;
    data_map_.erase(ts);
    data_[data_pt_ - 1].reset();
  }

  /// @brief order data by timestamp
  void _order_by_time() {
    std::sort(data_.begin(), data_.end(),
              [](const std::shared_ptr<dataclip_t>& a, const std::shared_ptr<dataclip_t>& b) {
                return a->timestamp.ns < b->timestamp.ns;
              });
  }

  /// @brief base directory
  const fs::path base_dir_;

  /// @brief data list
  std::vector<std::shared_ptr<dataclip_t>> data_;
  std::unordered_map<uint64_t, std::shared_ptr<dataclip_t>> data_map_;

  /// @brief data pointer
  size_t data_pt_ = 0;

};  // class Base

/**
 * @brief EuRoC dataset
 * @note [Get] Sequentially get data using next() function
 */
class EuRoC : public Base {
 public:
  /**
   * @brief Constructor
   * @param mav_dir mav directory of EuRoC dataset
   * @param motion_d load imu motion data (optional, true)
   * @param stereo_d load stereo cam1 data (optional, true)
   * @param auto_free auto free previous data (optional, false)
   * @param pre_load pre load all images (optional, false)
   * @note The EuRoC dataset is stored in a directory with the following structure:
   */
  explicit EuRoC(const std::string& mav_dir, const bool motion_d = true, const bool stereo_d = true,
                 const bool auto_free = false, const bool pre_load = false)
      : Base(mav_dir), motion_enabled_(motion_d), stereo_enabled_(stereo_d), auto_free_enabled_(auto_free) {
    if (__log_d__) {
      std::cout << "[dataset] load euroc dataset from " << mav_dir << std::endl;
      std::cout << "[euroc] imu motion " << (motion_d ? "enabled" : "disabled") << std::endl;
      std::cout << "[euroc] stereo " << (stereo_d ? "enabled" : "disabled") << std::endl;
    }

    _load_camera_data(stereo_enabled_);
    if (motion_enabled_) {
      _load_motion_data();
    }
    if (pre_load) {
      _pre_load();
    }
    _order_by_time();
  }

  /**
   * @brief get next dataclip
   * @note [Return] return nullptr if no next data available
   */
  std::shared_ptr<dataclip_t> next() override {
    if (data_pt_ >= data_.size()) {
      return nullptr;
    }
    auto data_at_pt = data_[data_pt_];
    // read for camera data
    const uint64_t ts = data_at_pt->timestamp.ns;
    // check avaliable for cam0 and load status
    if (data_at_pt->has_cam0() && data_at_pt->cam0.empty()) {
      data_at_pt->cam0 = _read_image(ts, 0);
    }
    // check avaliable for cam1 (stereo) and load status
    if (stereo_enabled_ && data_at_pt->available[dataclip_t::CAM1] && data_at_pt->cam1.empty()) {
      data_at_pt->cam1 = _read_image(ts, 1);
    }

    if (__log_d__) {
      std::cout << "[euroc] " << (data_pt_ + 1) << " / " << data_.size() << " at " << ts << std::endl;
    }

    if (auto_free_enabled_) {
      _free_last();
    }

    return data_[data_pt_++];
  }

 private:
  /// @brief load camera sensor calib and frame
  void _load_camera_data(bool stereo) {
    if (__log_d__) {
      std::cout << "[euroc] loading camera data" << std::endl;
    }

    for (int i = 0; i <= static_cast<int>(stereo); ++i) {
      fs::path cam_csv = base_dir_ / ("cam" + std::to_string(i)) / "data.csv";
      fs::path cam_data = base_dir_ / ("cam" + std::to_string(i)) / "data";
      fs::path cam_yaml = base_dir_ / ("cam" + std::to_string(i)) / "sensor.yaml";
      auto csv = csvReader(cam_csv.string());

      size_t cnt = 0;
      while (!csv.isEnd()) {
        auto ts = csv.next<uint64_t>()[0];
        csv.ignore();
        auto data = get_(ts, true);
        data->set(i);
        ++cnt;
      }

      if (__log_d__) {
        std::cout << "[euroc] loaded " << cnt << " frames for cam" << i << std::endl;
      }

      // read yaml
      YAML::Node node = YAML::LoadFile(cam_yaml.string());
      int width = node["resolution"][0].as<int>();
      int height = node["resolution"][1].as<int>();
      resolution_ = cv::Size(width, height);
      cam_rate_hz_ = node["rate_hz"].as<float>();
      auto intr_vec = vector_from_yaml<float>(node["intrinsics"]);
      intrinsic_[i] = intrinsic_t{.fx = intr_vec(0), .fy = intr_vec(1), .cx = intr_vec(2), .cy = intr_vec(3)};
      auto distor_vec = vector_from_yaml<double>(node["distortion_coefficients"]);
      distortion_[i] = distortion_t{.k1 = distor_vec(0),
                                    .k2 = distor_vec(1),
                                    .p1 = distor_vec(2),
                                    .p2 = distor_vec(3),
                                    .k3 = (distor_vec.size() > 4 ? distor_vec(4) : 0)};
    }
  }

  /// @brief load imu sensor calib and data
  void _load_motion_data() {
    if (__log_d__) {
      std::cout << "[euroc] loading imu motion data" << std::endl;
    }

    fs::path imu_csv = base_dir_ / "imu0" / "data.csv";
    auto csv = csvReader(imu_csv.string());

    size_t cnt = 0;
    while (!csv.isEnd()) {
      auto ts = csv.next<uint64_t>()[0];
      auto data = get_(ts, true);
      auto val = csv.next<double>(6);
      data->gyroscope = motion_t{val[0], val[1], val[2]};
      data->accelerometer = motion_t{val[3], val[4], val[5]};
      data->set(dataclip_t::GYRO);
      data->set(dataclip_t::ACC);
      ++cnt;
    }

    if (__log_d__) {
      std::cout << "[euroc] loaded " << cnt << " imu motion data" << std::endl;
    }

    // read yaml
    fs::path imu_yaml = base_dir_ / "imu0" / "sensor.yaml";
    YAML::Node node = YAML::LoadFile(imu_yaml.string());
    imu_rate_hz_ = node["rate_hz"].as<float>();
  }

  /// @brief read image data
  cv::Mat _read_image(const uint64_t ns, const int cam_id) {
    fs::path cam = base_dir_ / ("cam" + std::to_string(cam_id)) / "data" / (std::to_string(ns) + ".png");
    if (!fs::exists(cam)) {
      throw std::runtime_error("file not found: " + cam.string());
    }
    return cv::imread(cam.string(), cv::IMREAD_UNCHANGED);
  }

  /// @brief preload all images
  void _pre_load() {
    if (__log_d__) {
      std::cout << "[euroc] preloading all images" << std::endl;
    }

    size_t cnt = 0;
    for (const auto& d : data_) {
      if (d->available[dataclip_t::CAM0] && d->cam0.empty()) {
        d->cam0 = _read_image(d->timestamp.ns, 0);
        ++cnt;
      }
      if (stereo_enabled_ && d->available[dataclip_t::CAM1] && d->cam1.empty()) {
        d->cam1 = _read_image(d->timestamp.ns, 1);
        ++cnt;
      }
    }

    if (__log_d__) {
      std::cout << "[euroc] " << cnt << " images loaded" << std::endl;
    }
  }

  cv::Size resolution_;
  intrinsic_t intrinsic_[2];
  distortion_t distortion_[2];
  float cam_rate_hz_;
  float imu_rate_hz_;
  // Eigen::Matrix4f T_BS_[3];

  const bool motion_enabled_;
  const bool stereo_enabled_;
  const bool auto_free_enabled_;
};  // class EuRoC

}  // namespace dataset