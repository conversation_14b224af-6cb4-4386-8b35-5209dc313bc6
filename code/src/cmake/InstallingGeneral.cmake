include(GNUInstallDirs)

install(
        DIRECTORY 
                ${CMAKE_CURRENT_SOURCE_DIR}/rdvio/include/
                ${CMAKE_CURRENT_SOURCE_DIR}/rdvio_estimation/include/
                ${CMAKE_CURRENT_SOURCE_DIR}/rdvio_extra/include/
                ${CMAKE_CURRENT_SOURCE_DIR}/rdvio_geometry/include/
                ${CMAKE_CURRENT_SOURCE_DIR}/rdvio_map/include/
                ${CMAKE_CURRENT_SOURCE_DIR}/rdvio_util/include/
        DESTINATION include
        COMPONENT SourceCode
        FILES_MATCHING PATTERN "**/*.h*"
)

# NOTE(Jack): These two install commands go together. The first actually install the library (<blah>.so) and documents
# its dependencies wht the `EXPORT` command (or something like this). Then the second install command actually installs
# the dependency documentation to the share/cmake folder (or something like this).
install(
        TARGETS ${PROJECT_NAME}
        EXPORT "${PROJECT_NAME}Targets"
        TYPE LIBRARY
        COMPONENT Runtime
)
install(
        EXPORT "${PROJECT_NAME}Targets"
        DESTINATION lib/cmake
        COMPONENT SourceCode
)