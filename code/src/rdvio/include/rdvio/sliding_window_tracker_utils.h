#pragma once

#include <rdvio/map/frame.h>

namespace rdvio {

Pose ImuCameraPoseDelta(const Frame* const frame_i, const Frame* const frame_j) {
  auto camera = frame_i->camera;
  auto imu = frame_i->imu;

  matrix<4> Pwc = matrix<4>::Identity();
  matrix<4> PwI = matrix<4>::Identity();
  matrix<4> Pwi = matrix<4>::Identity();
  matrix<4> Pwj = matrix<4>::Identity();

  Pwc.block<3, 3>(0, 0) = camera.Q_body_sensor.toRotationMatrix();
  Pwc.block<3, 1>(0, 3) = camera.P_body_sensor;
  PwI.block<3, 3>(0, 0) = imu.Q_body_sensor.toRotationMatrix();
  PwI.block<3, 1>(0, 3) = imu.P_body_sensor;
  Pwi.block<3, 3>(0, 0) = frame_i->pose.q.toRotationMatrix();
  Pwi.block<3, 1>(0, 3) = frame_i->pose.p;
  Pwj.block<3, 3>(0, 0) = frame_j->pose.q.toRotationMatrix();
  Pwj.block<3, 1>(0, 3) = frame_j->pose.p;

  matrix<4> Pji = Pwj.inverse() * Pwi;

  matrix<4> P = (Pwc.inverse() * PwI * Pji * PwI.inverse() * Pwc);

  const matrix<3> R{P.block<3, 3>(0, 0)};
  const vector<3> t{P.block(0, 3, 3, 1)};

  return Pose(R, t);
}

matrix<3> compute_essential_matrix(const matrix<3>& R, const vector<3>& t) {
  matrix<3> t_ = matrix<3>::Zero();

  t_(0, 1) = -t(2);
  t_(0, 2) = t(1);
  t_(1, 0) = t(2);
  t_(1, 2) = -t(0);
  t_(2, 0) = -t(1);
  t_(2, 1) = t(0);

  matrix<3> E = t_ * R;
  return E;
}

matrix<3> compute_essential_matrix(const Pose& pose) {
  return compute_essential_matrix(pose.q.toRotationMatrix(), pose.p);
}

}  // namespace rdvio