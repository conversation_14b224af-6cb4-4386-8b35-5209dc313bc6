#pragma once

#include <mutex>
#include <optional>

#ifdef USE_MULTI_THREADING
#include <atomic>
#include <thread>
#endif

#include "rdvio/config.hpp"
#include "rdvio/estimation/state.h"
#include "rdvio/extra/image_frontend.hpp"
#include "rdvio/system_state_monitor.h"
#include "rdvio/types.h"

namespace rdvio {

class Config;
class FeatureTracker;
class Frame;
class Frontend;
class Image;
class Map;

class Handler {
  struct GyroscopeData {
    double t;
    vector<3> w;
  };
  struct AccelerometerData {
    double t;
    vector<3> a;
  };

 public:
  Handler(std::shared_ptr<Config> config);

  void TrackImu(const ImuData& imu);

  void TrackCamera(std::shared_ptr<extra::ImageFrontend> image, std::optional<const double> speed = std::nullopt);

  std::optional<KinematicState> GetLatestState() const;

  Pose PredictPose(const double& t, std::deque<ImuData>& imus) const;

  void PropagateState(double& state_time, const double t, const vector<3>& w, const vector<3>& a, PoseState& state_pose,
                      MotionState& state_motion) const;

  SystemState GetSystemState() const;

  Pose GetInitialGravityToCameraPose() const;

  std::deque<ImuData> GetImuQueue() const;

  std::vector<Eigen::Vector3d> GetTriangulatedLandmarks() const;

  std::vector<size_t> GetLandmarksTrackingCount() const;

  std::vector<Eigen::Vector2i> GetKeypoints() const;

  RigidBodyState GetRigidBodyState(const ReferenceFrame target_frame) const;

  double GetStateTimestamp() const;

  vector<3> GetLinearVelocity(const ReferenceFrame target_frame) const;

  vector<3> GetAngularVelocity(const ReferenceFrame target_frame) const;

  matrix<4> GetPose(const ReferenceFrame target_frame) const;

  void Exit();

 private:
#ifndef USE_MULTI_THREADING
  void Run();
#endif

  void InitializeStaticTransformations();

  vector<3> GetBodyLinearVelocity() const;

  vector<3> GetBodyAngularVelocity() const;

  matrix<4> GetRelativeBodyPose() const;

  std::shared_ptr<SystemStateMonitor> system_state_monitor_;

  std::shared_ptr<FeatureTracker> feature_tracker;
  std::shared_ptr<Frontend> frontend;

  std::mutex latest_mutex_;
  double latest_timestamp_ = 0.0;
  Pose latest_pose_;

  std::deque<GyroscopeData> gyroscopes;
  std::deque<AccelerometerData> accelerometers;

  std::deque<ImuData> imu_queue;
  std::deque<std::unique_ptr<Frame>> frames;

  std::shared_ptr<Config> config;

  // Static Transformations
  matrix<4> T_body_cam_;
  matrix<4> T_body_imu_;

  const std::string module_scope_{"Handler"};

#ifdef USE_MULTI_THREADING
  std::thread t_frontend_;
  std::thread t_feature_tracker_;
  std::atomic<bool> exit_ = false;
#endif
};

}  // namespace rdvio
