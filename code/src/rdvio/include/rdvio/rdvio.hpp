#pragma once

#include "rdvio/extra/camera.h"
#include "rdvio/extra/image_frontend.hpp"
#include "rdvio/extra/opencv_image.h"
#include "rdvio/geometry/transformations.hpp"
#include "rdvio/handler.h"
#include "rdvio/map/frame.h"

namespace rdvio {

const Eigen::Matrix4d T_cv_imu{(Eigen::Matrix4d() << 1, 0, 0, 0, 0, 0, -1, 0, 0, 1, 0, 0, 0, 0, 0, 1).finished()};
const Eigen::Matrix3d R_cv_imu{(Eigen::Matrix3d() << 1, 0, 0, 0, 0, -1, 0, 1, 0).finished()};

class Odometry {
 public:
  Odometry(const std::string& device_config_filename,
           const std::optional<std::string> slam_config_filename = std::nullopt) {
    this->config_ = std::make_shared<Config>(device_config_filename, slam_config_filename);

    Init();
  }

  Odometry(const YAML::Node& device_config, const std::optional<YAML::Node> slam_config = std::nullopt) {
    this->config_ = std::make_shared<Config>(device_config, slam_config);

    Init();
  }

  ~Odometry() = default;

  void Init() {
    const auto camera_config{config_->GetCameraConfig()};
    camera_ = std::make_shared<extra::Camera>(camera_config);
    handler_ = std::make_unique<Handler>(config_);
    T_body_cam_ =
        Pose(camera_config.camera_to_body_rotation, camera_config.camera_to_body_translation).AsTransformationMatrix();
  }

  /**
   * @brief Adds a new frame to the system with an associated timestamp and optional velocity information.
   *
   * This function processes an image frame captured at time `t` and integrates it into the system.
   * Optionally, a scalar velocity estimate can be provided to assist motion estimation.
   *
   * @param t The timestamp of the frame (in seconds).
   * @param image The image data in OpenCV's `cv::Mat` format.
   * @param speed (Optional, m/s) A scalar representing the speed estimate at the time of the frame.
   */
  void AddFrame(const double t, const cv::Mat& image, std::optional<const double> speed = std::nullopt) {
    // TODO(Victor/Jack): We might be cloning too much! We might need to not do this in the ros node anymore because
    // we handle it here now.
    cv::Mat img_gray{image.clone()};

    const size_t channels{static_cast<size_t>(image.channels())};

    if (channels == 3 || channels == 4) {
      cv::cvtColor(image, img_gray, channels == 3 ? cv::COLOR_BGR2GRAY : cv::COLOR_BGRA2GRAY);
    } else if (channels != 1) {
      throw std::runtime_error("Invalid image channel, must be 1, 3 or 4");
    }

    if (camera_->DoImageRectification()) {
      img_gray = camera_->RectifyImage(img_gray);
    }

    const OpticalFlowConfig optical_flow_config{config_->GetFeatureTrackerConfig().optical_flow_config};

    std::shared_ptr<extra::ImageFrontend> image_frontend{
        std::make_shared<extra::OpenCvImage>(img_gray, t, camera_, optical_flow_config, false)};

    speed ? handler_->TrackCamera(image_frontend, speed.value()) : handler_->TrackCamera(image_frontend);
  }

  void AddMotion(const double t, const Eigen::Vector3d& acc, const Eigen::Vector3d& gyro) {
    handler_->TrackImu({t, gyro, acc});
  }

  Eigen::Matrix4d TransformWorldCam() const {
    const auto state{handler_->GetLatestState()};

    auto Twb{state.has_value() ? state->pose.AsTransformationMatrix() : matrix<4>::Identity()};

    Twb = T_cv_imu * Twb * T_body_cam_;

    return Twb;
  }

  /**
   * @brief Returns a transformation from Odom frame (IMU frame at t=0) to IMU frame at t=t, which corresponds to the
   * relative pose estimation over time.
   *
   * TODO: (Vipul) Remove the const return and make the function const instead.
   */
  const Eigen::Matrix4d TransformOdomImu() { return handler_->GetPose(ReferenceFrame::BODY); }

  Eigen::Vector3d GetAngularBodyVelocity() const { return handler_->GetAngularVelocity(ReferenceFrame::BODY); }

  Eigen::Vector3d GetLinearBodyVelocity() const { return handler_->GetLinearVelocity(ReferenceFrame::BODY); }

  RigidBodyState GetRigidBodyState(const ReferenceFrame target_frame) const {
    return handler_->GetRigidBodyState(target_frame);
  }

  std::optional<KinematicState> GetKinematicState() const { return handler_->GetLatestState(); }

  double StateTimestamp() const { return handler_->GetStateTimestamp(); }

  SystemState State() const { return handler_->GetSystemState(); }

  std::vector<Eigen::Vector3d> LocalMap() const {
    auto points{std::move(handler_->GetTriangulatedLandmarks())};
    for (auto& p : points) {
      p = R_cv_imu * p;
    }
    return points;
  }

  /**
   * @brief Returns the number of frames a landmark was tracked across.
   */
  std::vector<size_t> GetLandmarksTrackingCount() const { return handler_->GetLandmarksTrackingCount(); }

  std::vector<Eigen::Vector2i> keypoints() const { return handler_->GetKeypoints(); }

  std::shared_ptr<Config> GetConfig() const {
    if (this->config_ == nullptr) {
      throw std::runtime_error("Failed to create yaml_config: shared pointer is null.");
    }
    return config_;
  }

  void Exit() const { handler_->Exit(); }

 private:
  std::unique_ptr<Handler> handler_;
  Eigen::Matrix4d T_body_cam_;

  std::shared_ptr<Config> config_;
  std::shared_ptr<extra::Camera> camera_;
};

}  // namespace rdvio