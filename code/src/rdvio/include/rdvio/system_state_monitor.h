#pragma once

#include <iostream>
#include <memory>
#include <optional>
#include <stdexcept>
#include <string>
#include <unordered_map>

#include "rdvio/types.h"

namespace rdvio {

class ComponentHandle;

enum class ComponentState { INITIALIZING, OK, CRITICAL };

class SystemStateMonitor {
 public:
  friend class ComponentHandle;

  std::string ComponentStateToString(const ComponentState state) const;

  std::shared_ptr<ComponentHandle> RegisterComponent(const std::string& debug_name,
                                                     const ComponentState initial_state = ComponentState::INITIALIZING);

  void UnregisterComponent(ComponentHandle* const handle);

  SystemState GetSystemState();

 private:
  void ReportState(const ComponentState state, const std::optional<std::string> message, ComponentHandle* const handle);

  void EstimateSystemState();

 private:
  std::unordered_map<ComponentHandle*, ComponentState> component_states_;
  SystemState system_state_;
};

class ComponentHandle {
 public:
  friend class SystemStateMonitor;

  ComponentHandle(const std::string& debug_name, SystemStateMonitor* parent);

  ~ComponentHandle();

  void ReportState(const ComponentState state, std::optional<std::string> message = std::nullopt);

  std::string GetDebugName() const;

 private:
  SystemStateMonitor* parent_;
  std::string debuge_name_;
};
}  // namespace rdvio