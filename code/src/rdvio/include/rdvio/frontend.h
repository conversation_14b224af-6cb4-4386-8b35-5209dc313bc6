#pragma once

#include <optional>

#include "rdvio/estimation/state.h"
#include "rdvio/system_state_monitor.h"
#include "rdvio/types.h"

namespace rdvio {

class Config;
class Frame;
class Initializer;
class FeatureTracker;
class SlidingWindowTracker;

class Frontend {
 public:
  Frontend(std::shared_ptr<FeatureTracker> _feature_tracker, std::shared_ptr<Config> _config,
           const std::shared_ptr<SystemStateMonitor> _system_monitor);
  ~Frontend();

  void run();

  bool QueryFeatureTracker();

  void InitializeFrontend(const size_t pending_frame_id);

  void Reset();

  std::vector<Eigen::Vector3d> GetTriangulatedLandmarks() const;

  std::vector<size_t> GetLandmarksTrackingCount() const;

  Pose GetInitialGravityToCameraPose() const;

 private:
  void SetLatestState(const KinematicState kinematic_state, const size_t latest_frame_id) {
    std::unique_lock lock(latest_state_mutex);
    latest_state = {kinematic_state, latest_frame_id};
    lock.unlock();
  }

  std::deque<size_t> pending_frame_ids;

  std::shared_ptr<SystemStateMonitor> system_state_monitor_;
  std::shared_ptr<ComponentHandle> component_monitor_;

  std::shared_ptr<FeatureTracker> feature_tracker;
  std::shared_ptr<Config> config;
  std::unique_ptr<Initializer> initializer;
  std::unique_ptr<SlidingWindowTracker> sliding_window_tracker;

  std::tuple<KinematicState, size_t> latest_state;
  mutable std::mutex latest_state_mutex;

  // This pose can transform a point from the Cam optical frame to the gravity aligned frame during initialization.
  Pose T_gravity_cam_0;
};

}  // namespace rdvio
