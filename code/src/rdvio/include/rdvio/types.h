#pragma once

#include <algorithm>
#include <atomic>
#include <chrono>
#include <condition_variable>
#include <deque>
#include <iostream>
#include <list>
#include <map>
#include <memory>
#include <mutex>
#include <queue>
#include <random>
#include <string>
#include <thread>
#include <unordered_map>
#include <vector>

#include <Eigen/Eigen>
#include <opencv2/core/eigen.hpp>
#include <opencv2/opencv.hpp>

namespace rdvio {

enum class SystemState { INITIALIZING, READY_TO_TRACK, TRACKING, CRASHED, UNKNOWN };

enum class ReferenceFrame { BODY, IMU, CAMERA };

std::string ToString(const SystemState& system_state);

enum class CameraModel { PINHOLE, RADTAN, EQUI, DOUBLE_SPHERE };
CameraModel ParseCameraModel(const std::string& camera_model);

template <typename T>
using map = Eigen::Map<T>;

template <typename T>
using const_map = Eigen::Map<const T>;

inline constexpr size_t nil() { return size_t(-1); }

template <typename T>
struct compare; /*
    constexpr bool operator()(const T &a, const T &b) const;
*/

template <typename T>
struct compare<T*> {
  constexpr bool operator()(const T* a, const T* b) const { return std::less<T>()(*a, *b); }
};

template <int Rows = Eigen::Dynamic, int Cols = Rows, bool UseRowMajor = false, typename T = double>
using matrix = typename std::conditional<Rows != 1 && Cols != 1,
                                         Eigen::Matrix<T, Rows, Cols, UseRowMajor ? Eigen::RowMajor : Eigen::ColMajor>,
                                         Eigen::Matrix<T, Rows, Cols>>::type;

template <int Dimension = Eigen::Dynamic, bool RowVector = false, typename T = double>
using vector =
    typename std::conditional<RowVector, matrix<1, Dimension, false, T>, matrix<Dimension, 1, false, T>>::type;

using quaternion = Eigen::Quaternion<double>;

const double kNominalGravity{9.80665};

const vector<3> kNominalGravityVector{0, 0, -kNominalGravity};

struct Twist {
  vector<3> linear_velocity;
  vector<3> angular_velocity;

  Twist(const vector<3>& linear_velocity, const vector<3>& angular_velocity)
      : linear_velocity{linear_velocity}, angular_velocity{angular_velocity} {}
};

struct Feature {
  vector<3> bearing;

  Feature() { bearing.setZero(); }
  Feature(const vector<3>& bearing_) : bearing(bearing_) {}
};

struct ImuData {
  double t;
  vector<3> w;
  vector<3> a;
};

struct Pose {
  Pose() {
    q.setIdentity();
    p.setZero();
  }

  Pose(const matrix<3>& rotation, const vector<3>& translation) : q{rotation}, p{translation} { q.normalize(); }

  Pose(const quaternion& rotation, const vector<3>& translation) : q{rotation}, p{translation} { q.normalize(); }

  Pose(const matrix<4>& transformation_matrix)
      : q{transformation_matrix.block<3, 3>(0, 0)}, p{transformation_matrix.block<3, 1>(0, 3)} {
    q.normalize();
  }

  matrix<4> AsTransformationMatrix() const {
    matrix<4> transform{Eigen::Matrix4d::Identity()};
    transform.block<3, 3>(0, 0) = q.toRotationMatrix();
    transform.block<3, 1>(0, 3) = p;

    return transform;
  }

  quaternion q;
  vector<3> p;
};

using uchar = unsigned char;

struct RigidBodyState {
  double timestamp;
  Pose pose;
  Twist twist;
};

#define synchronized(obj_ptr) if constexpr (auto local_synchronized_lock__ = (obj_ptr)->lock(); true)

}  // namespace rdvio