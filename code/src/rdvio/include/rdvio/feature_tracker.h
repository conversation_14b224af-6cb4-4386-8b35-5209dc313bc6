#pragma once

#include <optional>

#include "rdvio/config.hpp"
#include "rdvio/estimation/state.h"
#include "rdvio/system_state_monitor.h"
#include "rdvio/types.h"

namespace rdvio {
class Frontend;

class FeatureTracker {
 public:
  FeatureTracker(const FeatureTrackerConfig& config, const std::shared_ptr<SystemStateMonitor> system_monitor);
  ~FeatureTracker();

  bool empty() const { return frames.empty(); }

  void run();

  void track_frame(std::unique_ptr<Frame> frame);

  std::vector<Eigen::Vector2i> get_keypoints() const;

  std::optional<KinematicState> get_latest_state() const;

  std::vector<Frame*> GetFramesToIssue() {
    const auto frames_to_be_issued_copy{frames_to_be_issued};
    frames_to_be_issued.clear();
    return frames_to_be_issued_copy;
  }

  void set_latest_front_end_state(const std::tuple<KinematicState, size_t>& frontend_state) {
    std::unique_lock lock(latest_frontend_state_mutex);
    latest_frontend_state = frontend_state;
  }

  std::shared_ptr<Map> map;
  std::unique_ptr<Map> keymap;

 private:
  std::shared_ptr<ComponentHandle> component_monitor_;
  const FeatureTrackerConfig config_;
  std::deque<std::unique_ptr<Frame>> frames;
  // TODO(Jack): It seems like we should be able to only have one state. However as part of the refactor to eliminate
  // the circular dependency of the feature tracker on the frontend, while changing as little of the code as possible,
  // we needed to add the latest_frontend_state here, that is set externally from the front end. In the future we can
  // probably just use a single latest state, but as we have no test or ability to prove this, we simply add the extra
  // state here.
  std::optional<KinematicState> latest_state;
  std::tuple<KinematicState, size_t> latest_frontend_state;
  mutable std::mutex latest_state_mutex;
  mutable std::mutex latest_frontend_state_mutex;
  std::vector<Frame*> frames_to_be_issued;
};

}  // namespace rdvio
