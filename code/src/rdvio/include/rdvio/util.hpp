#pragma once

#include <chrono>
#include <iostream>
#include <string>

#include <spl-logging/logging.hpp>

namespace rdvio::utils {

// NOTE (Vipul) Unused class but seems good to measure the computation time of the function.
class FunctionFrequency {
 public:
  static inline void calculateFrequency() {
    static int callCount = 0;                                          // Static variable to count calls
    static auto lastTime = std::chrono::high_resolution_clock::now();  // Static variable to store the last call time

    ++callCount;

    // Get the current time
    auto currentTime = std::chrono::high_resolution_clock::now();

    // Calculate the time difference since the last call
    std::chrono::duration<double> timeDiff = currentTime - lastTime;

    if (timeDiff.count() > 0) {                   // Avoid division by zero
      double frequency = 1.0 / timeDiff.count();  // Frequency in Hz
      std::cout << "Function called " << callCount << " times. Frequency: " << frequency << " Hz" << std::endl;
    } else {
      std::cout << "Function called " << callCount << " times. Frequency: Infinite (very fast)" << std::endl;
    }

    // Update the last time to the current time
    lastTime = currentTime;
  }
};

template <typename... MessageParameterType>
void LogStatistics(const std::string& logger_name, const std::string& log_message,
                   const MessageParameterType&... message_parameters) {
#ifdef LOG_STATISTICS
  const auto formatted_msg{FormatString(log_message, message_parameters...)};
  // NOTE (Vipul): Apparently if you pass kDebug to the function, the spl-log doesn't work.
  spl::logging::Log(logger_name, formatted_msg, spl::logging::LoggingLevel::kInfo);
#endif
}

}  // namespace rdvio::utils