#include "rdvio/feature_tracker.h"

#include "rdvio/estimation/solver.h"
#include "rdvio/geometry/stereo.h"
#include "rdvio/handler.h"
#include "rdvio/map/frame.h"
#include "rdvio/map/map.h"
#include "rdvio/map/track.h"
#include "rdvio/types.h"
#include "rdvio/util.hpp"

namespace rdvio {

FeatureTracker::FeatureTracker(const FeatureTrackerConfig& config,
                               const std::shared_ptr<SystemStateMonitor> system_monitor)
    : config_(config) {
  map = std::make_unique<Map>();
  keymap = std::make_unique<Map>();
  component_monitor_ = system_monitor->RegisterComponent("FeatureTracker");
}

FeatureTracker::~FeatureTracker() = default;

void FeatureTracker::run() {
  constexpr auto logger_name{"FeatureTracker::run"};
  constexpr double imu_time_threshold{1.0e-5};

  if (empty()) {
    return;
  }
#ifdef ENABLE_TIMING
  auto start = std::chrono::high_resolution_clock::now();
#endif
  std::unique_ptr<Frame> frame = std::move(frames.front());
  frames.pop_front();
  utils::LogStatistics(logger_name, "Processing frame ID: {} at timestamp: {}, we still have {} frames", frame->id(),
                       frame->image->t, frames.size());

  frame->image->PreProcess(config_.clahe_config);

  std::unique_lock lock(latest_frontend_state_mutex);
  const auto [kinematic_state, latest_optimized_frame_id] = latest_frontend_state;
  lock.unlock();
  const auto [latest_optimized_time, latest_optimized_pose, latest_optimized_motion] = kinematic_state;
  utils::LogStatistics(logger_name, "Latest keyframe optimized frame id: {}, with time: {}", latest_optimized_frame_id,
                       latest_optimized_time);

  const bool is_initialized{latest_optimized_frame_id != nil()};
  const bool sliding_window_frame_tag{not is_initialized or
                                      (frame->id() % config_.sliding_window_tracker_frequent == 0)};

  synchronized(map) {
    if (map->frame_num() > 0) {
      if (is_initialized) {
        size_t latest_optimized_frame_index = map->frame_index_by_id(latest_optimized_frame_id);
        if (latest_optimized_frame_index != nil()) {
          Frame* latest_optimized_frame = map->get_frame(latest_optimized_frame_index);
          latest_optimized_frame->pose = latest_optimized_pose;
          latest_optimized_frame->motion = latest_optimized_motion;
          for (size_t j = latest_optimized_frame_index + 1; j < map->frame_num(); ++j) {
            Frame* frame_i = map->get_frame(j - 1);
            Frame* frame_j = map->get_frame(j);
            frame_j->preintegration.integrate(frame_j->image->t, frame_i->motion.bias_gyroscope,
                                              frame_i->motion.bias_accelerometer, false, false);
            frame_j->preintegration.predict(frame_i, frame_j);
          }
        } else {
          spl::logging::Warning(logger_name, "Frame has slided out, system may be lost");
          std::unique_lock lk(latest_state_mutex);
          latest_state.reset();
        }
      }
      // This is the last frame in the map, which is the previous frame to the current frame and processed by the
      // frontend
      Frame* last_frame = map->get_frame(map->frame_num() - 1);
      if (!last_frame->preintegration.data.empty()) {
        if (frame->preintegration.data.empty() ||
            (frame->preintegration.data.front().t - last_frame->image->t > imu_time_threshold)) {
          ImuData imu = last_frame->preintegration.data.back();
          imu.t = last_frame->image->t;
          frame->preintegration.data.insert(frame->preintegration.data.begin(), imu);
        }
      }
      utils::LogStatistics(logger_name, "Frame with id {} has {} data for preintegration", frame->id(),
                           frame->preintegration.data.size());
      if (frame->preintegration.data.empty()) {
        spl::logging::Warning(logger_name, "Frame with id {} has no data for preintegration", frame->id());
      }
      frame->preintegration.integrate(frame->image->t, last_frame->motion.bias_gyroscope,
                                      last_frame->motion.bias_accelerometer, false, false);

      last_frame->track_keypoints(frame.get(), config_);
      if (is_initialized) {
        frame->preintegration.predict(last_frame, frame.get());

        std::unique_lock lk(latest_state_mutex);
        latest_state = {frame->image->t, frame->pose, frame->motion};
        lk.unlock();

        utils::LogStatistics(logger_name, "Updated latest state with id: {}, timestamp: {}, position: [{},{},{}]",
                             frame->id(), frame->image->t, frame->pose.p.x(), frame->pose.p.y(), frame->pose.p.z());

        component_monitor_->ReportState(ComponentState::OK);
      }
      last_frame->image->ReleaseImageBuffer();
    }

    if (sliding_window_frame_tag) {
      frame->detect_keypoints(config_);
    }
    map->attach_frame(std::move(frame));

    const size_t statistics_map_size_before_remove{map->frame_num()};
    while (map->frame_num() > (is_initialized ? config_.max_frames : config_.max_init_frames) &&
           map->get_frame(0)->id() < latest_optimized_frame_id) {
      map->erase_frame(0);
    }
    const size_t statistics_map_size_after_remove{map->frame_num()};
    utils::LogStatistics(logger_name, "Map has {} frames, {} frames removed from map", statistics_map_size_after_remove,
                         statistics_map_size_before_remove - statistics_map_size_after_remove);
  }
  if (sliding_window_frame_tag) {
    frames_to_be_issued.push_back(map->get_frame(map->frame_num() - 1));
    utils::LogStatistics(logger_name, "Frame id {} added to sliding window buffer, now containing {} frames",
                         frames_to_be_issued.back()->id(), frames_to_be_issued.size());
  }
#ifdef ENABLE_TIMING
  auto end = std::chrono::high_resolution_clock::now();
  auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
  utils::LogStatistics(logger_name, "FeatureTracker::run() took {} ms", elapsed.count());
#endif
}

void FeatureTracker::track_frame(std::unique_ptr<Frame> frame) { frames.emplace_back(std::move(frame)); }

std::optional<KinematicState> FeatureTracker::get_latest_state() const {
  std::unique_lock lk(latest_state_mutex);
  return latest_state;
}

std::vector<Eigen::Vector2i> FeatureTracker::get_keypoints() const {
  std::vector<Eigen::Vector2i> keypoints;
  auto frame = map->get_frame(map->frame_num() - 1);
  keypoints.reserve(frame->keypoint_num());
  for (size_t i = 0; i < keymap->frame_num(); ++i) {
    if (frame->get_track(i)) {
      keypoints.emplace_back(PinholeProject(frame->GetBearing(i), frame->K).cast<int>());
    }
  }
  return keypoints;
}

}  // namespace rdvio
