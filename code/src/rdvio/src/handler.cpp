#include "rdvio/handler.h"

#include <spl-logging/logging.hpp>

#include "rdvio/estimation/solver.h"
#include "rdvio/extra/image_frontend.hpp"
#include "rdvio/feature_tracker.h"
#include "rdvio/frontend.h"
#include "rdvio/geometry/lie_algebra.h"
#include "rdvio/geometry/stereo.h"
#include "rdvio/geometry/transformations.hpp"
#include "rdvio/map/frame.h"
#include "rdvio/map/map.h"
#include "rdvio/types.h"

namespace rdvio {

Handler::Handler(std::shared_ptr<Config> config) : config(config) {
  // Shared Monitor across Components
  system_state_monitor_ = std::make_shared<SystemStateMonitor>();

  Solver::Init(config->GetSolverConfig());

  feature_tracker = std::make_shared<FeatureTracker>(config->GetFeatureTrackerConfig(), system_state_monitor_);
  frontend = std::make_shared<Frontend>(feature_tracker, config, system_state_monitor_);

  InitializeStaticTransformations();

#ifdef USE_MULTI_THREADING
  std::cout << "[handler] multi-threading enabled" << std::endl;
  t_frontend_ = std::thread([this] {
    while (!exit_) {
      frontend->run();
      std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
  });

  t_feature_tracker_ = std::thread([this] {
    while (!exit_) {
      feature_tracker->run();
      std::this_thread::sleep_for(std::chrono::milliseconds(5));
    }
  });
#endif
}

void Handler::InitializeStaticTransformations() {
  const auto camera_config{config->GetCameraConfig()};
  T_body_cam_ =
      Pose(camera_config.camera_to_body_rotation, camera_config.camera_to_body_translation).AsTransformationMatrix();

  const auto imu_config{config->GetImuConfig()};
  T_body_imu_ = Pose(imu_config.imu_to_body_rotation, imu_config.imu_to_body_translation).AsTransformationMatrix();
}

#ifndef USE_MULTI_THREADING
void Handler::Run() {
  feature_tracker->run();
  frontend->run();
}
#endif

void Handler::TrackCamera(std::shared_ptr<extra::ImageFrontend> image, std::optional<const double> speed) {
  const auto imu_config{config->GetImuConfig()};
  const auto camera_config{config->GetCameraConfig()};

  frames.emplace_back(MakeFrameFromConfig(camera_config, imu_config, image, speed));

#ifndef USE_MULTI_THREADING
  this->Run();
#endif
}

void Handler::TrackImu(const ImuData& imu) {
  imu_queue.emplace_back(imu);
  while (imu_queue.size() > 0 && frames.size() > 0) {
    if (imu_queue.front().t <= frames.front()->image->t) {
      frames.front()->preintegration.data.push_back(imu_queue.front());
      imu_queue.pop_front();
    } else {
      feature_tracker->track_frame(std::move(frames.front()));
      frames.pop_front();
    }
  }
}

Pose Handler::PredictPose(const double& t, std::deque<ImuData>& imu_queue) const {
  const auto state{feature_tracker->get_latest_state()};
  if (state.has_value()) {
    const auto [state_time, state_pose, state_motion] = state.value();

    while (!imu_queue.empty() && imu_queue.front().t <= state_time) {
      imu_queue.pop_front();
    }

    double propagated_time{state_time};
    PoseState propagated_pose{state_pose};
    MotionState propagated_motion{state_motion};
    for (const auto& imu : imu_queue) {
      if (imu.t <= t) {
        PropagateState(propagated_time, imu.t, imu.w, imu.a, propagated_pose, propagated_motion);
      }
    }
    return propagated_pose;
  } else {
    return Pose{};
  }
}

void Handler::PropagateState(double& state_time, const double t, const vector<3>& w, const vector<3>& a,
                             PoseState& state_pose, MotionState& state_motion) const {
  const double dt{t - state_time};
  state_pose.p = state_pose.p + dt * state_motion.linear_velocity +
                 0.5 * dt * dt * (kNominalGravityVector + state_pose.q * (a - state_motion.bias_accelerometer));
  state_motion.linear_velocity = state_motion.linear_velocity +
                                 dt * (kNominalGravityVector + state_pose.q * (a - state_motion.bias_accelerometer));
  state_pose.q = (state_pose.q * expmap((w - state_motion.bias_gyroscope) * dt)).normalized();
  state_time = t;
}

SystemState Handler::GetSystemState() const { return system_state_monitor_->GetSystemState(); }

Pose Handler::GetInitialGravityToCameraPose() const { return frontend->GetInitialGravityToCameraPose(); }

std::optional<KinematicState> Handler::GetLatestState() const {
  const auto latest_state{feature_tracker->get_latest_state()};
  if (latest_state.has_value()) {
    return latest_state;
  } else {
    return std::nullopt;
  }
}

std::vector<Eigen::Vector3d> Handler::GetTriangulatedLandmarks() const { return frontend->GetTriangulatedLandmarks(); }

std::vector<size_t> Handler::GetLandmarksTrackingCount() const { return frontend->GetLandmarksTrackingCount(); }

std::vector<Eigen::Vector2i> Handler::GetKeypoints() const { return feature_tracker->get_keypoints(); }

std::deque<ImuData> Handler::GetImuQueue() const { return imu_queue; }

RigidBodyState Handler::GetRigidBodyState(const ReferenceFrame target_frame) const {
  const auto timestamp{GetStateTimestamp()};
  const auto pose{GetPose(target_frame)};
  const auto linear_velocity{GetLinearVelocity(target_frame)};
  const auto angular_velocity{GetAngularVelocity(target_frame)};

  const Twist twist{linear_velocity, angular_velocity};
  const RigidBodyState state{timestamp, pose, twist};

  return state;
}

double Handler::GetStateTimestamp() const {
  const auto state{GetLatestState()};

  return state.has_value() ? state->timestamp : 0.0;
}

matrix<4> Handler::GetPose(const ReferenceFrame target_frame) const {
  const auto T_body_t0_body_t{GetRelativeBodyPose()};
  switch (target_frame) {
    case ReferenceFrame::BODY:
      return T_body_t0_body_t;
    case ReferenceFrame::CAMERA:
      return T_body_cam_.inverse() * T_body_t0_body_t * T_body_cam_;
    case ReferenceFrame::IMU:
      return T_body_imu_.inverse() * T_body_t0_body_t * T_body_imu_;
    default:
      spl::logging::Critical(module_scope_ + ":GetPose", "Undefined reference frame. Returning identity pose");
      return matrix<4>::Identity();
  }
}

vector<3> Handler::GetLinearVelocity(const ReferenceFrame target_frame) const {
  const auto body_linear_velocity{GetBodyLinearVelocity()};
  const auto body_angular_velocity{GetBodyAngularVelocity()};
  switch (target_frame) {
    case ReferenceFrame::BODY:
      return body_linear_velocity;
    case ReferenceFrame::CAMERA:
      return geometry::TransformLinearVelocity(T_body_cam_, body_linear_velocity, body_angular_velocity);
    case ReferenceFrame::IMU:
      return geometry::TransformLinearVelocity(T_body_imu_, body_linear_velocity, body_angular_velocity);
    default:
      spl::logging::Critical(module_scope_ + ":GetLinearVelocity",
                             "Undefined reference frame. Returning zero linear velocity");
      return vector<3>::Zero();
  }
}

vector<3> Handler::GetAngularVelocity(const ReferenceFrame target_frame) const {
  const auto body_angular_velocity{GetBodyAngularVelocity()};
  switch (target_frame) {
    case ReferenceFrame::BODY:
      return body_angular_velocity;
    case ReferenceFrame::CAMERA:
      return geometry::TransformAngularVelocity(T_body_cam_, body_angular_velocity);
    case ReferenceFrame::IMU:
      return geometry::TransformAngularVelocity(T_body_imu_, body_angular_velocity);
    default:
      spl::logging::Critical(module_scope_ + ":GetAngularVelocity",
                             "Undefined reference frame. Returning zero angular velocity");
      return vector<3>::Zero();
  }
}

vector<3> Handler::GetBodyLinearVelocity() const {
  const auto state{GetLatestState()};
  if (state.has_value()) {
    // The pose (.q) is the rotation of the body frame w.r.t the world frame (gravity aligned frame).
    const auto pose_world_body{state->pose};
    // The velocity (.v) is the velocity of the body frame  w.r.t the world frame.
    const auto velocity_world{state->motion.linear_velocity};
    return pose_world_body.q.inverse() * velocity_world;
  } else {
    return Eigen::Vector3d::Zero();
  }
}

vector<3> Handler::GetBodyAngularVelocity() const {
  const auto state{GetLatestState()};
  if (state.has_value()) {
    const MotionState motion{state->motion};
    return motion.angular_velocity;
  } else {
    return Eigen::Vector3d::Zero();
  }
}

matrix<4> Handler::GetRelativeBodyPose() const {
  // Get the transformation that can transform a point from the Cam optical frame to the gravity aligned frame.
  const auto T_gravity_cam_0{GetInitialGravityToCameraPose().AsTransformationMatrix()};

  // Get latest transform from gravity to Body frame at time t
  const auto state{GetLatestState()};
  const matrix<4> T_gravity_body_t{state.has_value() ? state->pose.AsTransformationMatrix() : matrix<4>::Identity()};

  // Compute transform from Body frame at timestamp 0 to Body frame at timestamp t
  const auto T_body_0_body_t{T_body_cam_ * T_gravity_cam_0.inverse() * T_gravity_body_t};

  return T_body_0_body_t;
}

void Handler::Exit() {
#ifdef USE_MULTI_THREADING
  exit_ = true;

  if (t_frontend_.joinable()) {
    t_frontend_.join();
  }
  if (t_feature_tracker_.joinable()) {
    t_feature_tracker_.join();
  }
  std::cout << "[handler] multi-threading exited" << std::endl;
#endif
}

}  // namespace rdvio
