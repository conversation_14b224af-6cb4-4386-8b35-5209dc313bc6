#include "rdvio/frontend.h"

#include <iostream>

#include "rdvio/feature_tracker.h"
#include "rdvio/initialization/initializer.h"
#include "rdvio/map/frame.h"
#include "rdvio/map/map.h"
#include "rdvio/map/track.h"
#include "rdvio/sliding_window_tracker.h"
#include "rdvio/types.h"

namespace rdvio {

Frontend::Frontend(std::shared_ptr<FeatureTracker> _feature_tracker, std::shared_ptr<Config> _config,
                   const std::shared_ptr<SystemStateMonitor> _system_monitor)
    : feature_tracker(_feature_tracker), config(_config) {
  system_state_monitor_ = _system_monitor;
  component_monitor_ = system_state_monitor_->RegisterComponent("Frontend");

  this->Reset();
}

Frontend::~Frontend() = default;

void Frontend::run() {
  if (const bool frames_available{QueryFeatureTracker()}; not frames_available) {
    return;
  }

#ifdef <PERSON>E_TIMING
  const auto start = std::chrono::high_resolution_clock::now();
#endif

  const size_t latest_frame_id{pending_frame_ids.front()};
  if (initializer) {
    component_monitor_->ReportState(ComponentState::INITIALIZING);

    pending_frame_ids.clear();
    synchronized(feature_tracker->map) { initializer->MirrorKeyframeMap(feature_tracker->map.get(), latest_frame_id); }

    if (auto initialized_map{initializer->Initialize()}; initialized_map) {
      sliding_window_tracker = std::make_unique<SlidingWindowTracker>(
          std::move(initialized_map), config->GetSlidingWindowTrackerConfig(), system_state_monitor_);
      // The initialization process was successful and we will start attempting tracking in the next iteration
      this->InitializeFrontend(latest_frame_id);
    }
  } else if (sliding_window_tracker) {
    pending_frame_ids.pop_front();
    synchronized(feature_tracker->map) {
      sliding_window_tracker->MirrorFrame(feature_tracker->map.get(), latest_frame_id);
    }

    if (const bool successful_tracking{sliding_window_tracker->track()}; successful_tracking) {
      component_monitor_->ReportState(ComponentState::OK);

      const KinematicState kinematic_state{sliding_window_tracker->GetLatestState()};
      this->SetLatestState(kinematic_state, latest_frame_id);
    } else {
      component_monitor_->ReportState(ComponentState::CRITICAL, "Tracking failed, must reinitialize.");

      this->Reset();
    }
  }

  return;

#ifdef ENABLE_TIMING
  const auto end = std::chrono::high_resolution_clock::now();
  const auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
  std::cout << "Frontend::run() " << elapsed.count() << " ms" << std::endl;
#endif
}

bool Frontend::QueryFeatureTracker() {
  std::unique_lock lock(latest_state_mutex);
  feature_tracker->set_latest_front_end_state(latest_state);
  lock.unlock();

  const std::vector<Frame*> newly_issued_frames{feature_tracker->GetFramesToIssue()};
  for (const auto& frame : newly_issued_frames) {
    pending_frame_ids.push_back(frame->id());
  }

  if (std::empty(pending_frame_ids)) {
    return false;
  } else {
    return true;
  }
}

void Frontend::InitializeFrontend(const size_t latest_frame_id) {
  T_gravity_cam_0 = initializer->GetInitialGravityToCameraPose();
  sliding_window_tracker->feature_tracking_map = feature_tracker->map;

  const KinematicState kinematic_state{sliding_window_tracker->GetLatestState()};
  this->SetLatestState(kinematic_state, latest_frame_id);

  initializer.reset();
}

void Frontend::Reset() {
  this->SetLatestState({{}, {}, {}}, nil());

  initializer = std::make_unique<Initializer>(config->GetInitializerConfig(), system_state_monitor_);
  sliding_window_tracker.reset();
}

std::vector<Eigen::Vector3d> Frontend::GetTriangulatedLandmarks() const {
  if (sliding_window_tracker) {
    return sliding_window_tracker->GetTriangulatedLandmarks();
  }
  return {};
}

std::vector<size_t> Frontend::GetLandmarksTrackingCount() const {
  if (sliding_window_tracker) {
    return sliding_window_tracker->GetLandmarksTrackingCount();
  }
  return {};
}

Pose Frontend::GetInitialGravityToCameraPose() const { return T_gravity_cam_0; }

}  // namespace rdvio
