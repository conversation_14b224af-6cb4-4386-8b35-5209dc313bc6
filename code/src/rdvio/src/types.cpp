#include <rdvio/types.h>

namespace rdvio {

std::string ToString(const SystemState& system_state) {
  const static std::unordered_map<SystemState, std::string> system_state_map{
      {SystemState::INITIALIZING, "INITIALIZING"},
      {SystemState::READY_TO_TRACK, "READY_TO_TRACK"},
      {SystemState::TRACKING, "TRACKING"},
      {SystemState::CRASHED, "CRASHED"},
      {SystemState::UNKNOWN, "UNKNOWN"}};

  const auto it{system_state_map.find(system_state)};
  if (it != std::end(system_state_map)) {
    return it->second;
  } else {
    throw std::runtime_error("Unsupported System State.");
  }
}

CameraModel ParseCameraModel(const std::string& camera_model) {
  const static std::unordered_map<std::string, CameraModel> camera_model_map{{"none", CameraModel::PINHOLE},
                                                                             {"radtan", CameraModel::RADTAN},
                                                                             {"equi", CameraModel::EQUI},
                                                                             {"ds", CameraModel::DOUBLE_SPHERE}};

  const auto it{camera_model_map.find(camera_model)};
  if (it != camera_model_map.end()) {
    return it->second;
  } else {
    throw std::runtime_error("Unsupported camera model provided.");
  }
}

}  // namespace rdvio