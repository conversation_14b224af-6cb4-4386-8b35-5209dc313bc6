#include "rdvio/system_state_monitor.h"

#include <iostream>
#include <memory>
#include <optional>
#include <stdexcept>
#include <string>
#include <unordered_map>
#include <vector>

#include <spl-logging/logging.hpp>

namespace rdvio {

std::string SystemStateMonitor::ComponentStateToString(const ComponentState state) const {
  const std::unordered_map<ComponentState, std::string> state_map = {{ComponentState::INITIALIZING, "INITIALIZING"},
                                                                     {ComponentState::OK, "OK"},
                                                                     {ComponentState::CRITICAL, "CRITICAL"}};

  const auto it{state_map.find(state)};
  if (it != state_map.end()) {
    return it->second;
  }

  return std::string("UNKNOWN");
}

std::shared_ptr<ComponentHandle> SystemStateMonitor::RegisterComponent(const std::string& debug_name,
                                                                       const ComponentState initial_state) {
  const auto handle{std::make_shared<ComponentHandle>(debug_name, this)};
  component_states_.emplace(handle.get(), initial_state);

  spl::logging::Debug("SystemStateMonitor::RegisterComponent", "Registered " + debug_name);

  return handle;
}

void SystemStateMonitor::UnregisterComponent(ComponentHandle* const handle) {
  const auto it{component_states_.find(handle)};
  if (it == component_states_.end()) {
    return;
  }
  spl::logging::Debug("SystemStateMonitor::UnregisterComponent", "Unregistered " + handle->GetDebugName());

  component_states_.erase(it);
}

SystemState SystemStateMonitor::GetSystemState() {
  EstimateSystemState();
  return system_state_;
}

void SystemStateMonitor::ReportState(const ComponentState state, const std::optional<std::string> message,
                                     ComponentHandle* const handle) {
  const auto it{component_states_.find(handle)};
  if (it == component_states_.end()) {
    throw std::runtime_error("reportState called with unknown ComponentHandle.");
  }
  if (state != ComponentState::OK and state != ComponentState::INITIALIZING) {
    spl::logging::Debug("SystemStateMonitor::ReportState",
                        handle->GetDebugName() + " reports " + ComponentStateToString(state));
    if (message) {
      spl::logging::Debug("SystemStateMonitor::ReportState", handle->GetDebugName() + ": " + message.value());
    }
  }

  it->second = state;
}

void SystemStateMonitor::EstimateSystemState() {
  using ComponentEntry = std::tuple<const ComponentHandle*, std::string>;

  std::vector<ComponentEntry> critical;
  std::vector<ComponentEntry> initializing;
  std::vector<ComponentEntry> ok;

  for (const auto& [handle, state] : component_states_) {
    const ComponentEntry entry{handle, handle->GetDebugName()};
    switch (state) {
      case ComponentState::CRITICAL:
        critical.push_back(entry);
        break;
      case ComponentState::INITIALIZING:
        initializing.push_back(entry);
        break;
      case ComponentState::OK:
      default:
        ok.push_back(entry);
        break;
    }
  }

  bool swt_critical{false};
  bool ft_critical{false};
  for (const auto& e : critical) {
    const std::string& name{std::get<1>(e)};
    if (name == "SWTLocalizer") {
      swt_critical = true;
    }
    if (name == "SlidingWindowTracker") {
      ft_critical = true;
    }
  }

  SystemState system_state{SystemState::TRACKING};
  if (swt_critical and ft_critical) {
    system_state = SystemState::CRASHED;
  } else if (!initializing.empty()) {
    system_state = SystemState::INITIALIZING;
  }

  system_state_ = system_state;
}

ComponentHandle::ComponentHandle(const std::string& debug_name, SystemStateMonitor* const parent)
    : parent_(parent), debuge_name_(debug_name) {}

ComponentHandle::~ComponentHandle() {
  if (parent_) {
    parent_->UnregisterComponent(this);
  }
}

void ComponentHandle::ReportState(const ComponentState state, std::optional<std::string> message) {
  if (not parent_) {
    throw std::runtime_error("Invalid ComponentHandle: no associated SystemState.");
  }
  parent_->ReportState(state, message, this);
}

std::string ComponentHandle::GetDebugName() const { return debuge_name_; }

}  // namespace rdvio