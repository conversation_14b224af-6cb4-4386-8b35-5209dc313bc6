#include <gtest/gtest.h>

#include <spl-camera-models/intrinsic_config.hpp>

#include "rdvio/config.hpp"
#include "rdvio/types.h"
#include "rdvio/util/yaml.hpp"

using namespace rdvio;

class DefaultConfigFixture : public ::testing::Test {
 protected:
  DefaultConfigFixture() : sensor_config_path_("/testroot/code/test/test_data/euroc/euroc.yaml") {};

  void TestFeatureConfig(const Config& config, const size_t expected_max_frames = 200) {
    const auto feature_config{config.GetFeatureTrackerConfig()};

    ASSERT_NEAR(feature_config.min_keypoint_distance, 20.0, 1e-6);
    ASSERT_EQ(feature_config.max_keypoint_detection, 150);
    ASSERT_EQ(feature_config.max_init_frames, 60);
    ASSERT_EQ(feature_config.max_frames, expected_max_frames);
    ASSERT_TRUE(feature_config.predict_keypoints);
    ASSERT_NEAR(feature_config.rotation_misalignment_threshold, 0.1, 1e-6);
    ASSERT_NEAR(feature_config.angle_percentile, 0.7, 1e-6);
    ASSERT_NEAR(feature_config.rotation_ransac_threshold, 10, 1e-6);
    ASSERT_EQ(feature_config.sliding_window_tracker_frequent, 1);
    const auto clahe_config = feature_config.clahe_config;
    ASSERT_NEAR(clahe_config.clip_limit, 6.0, 1e-6);
    ASSERT_EQ(clahe_config.width, 8);
    ASSERT_EQ(clahe_config.height, 8);
    const auto optical_flow_config = feature_config.optical_flow_config;
    ASSERT_EQ(optical_flow_config.level_num, 3);
    ASSERT_EQ(optical_flow_config.max_iterations, 30);
    ASSERT_NEAR(optical_flow_config.epsilon, 0.01, 1e-6);
    ASSERT_EQ(optical_flow_config.window_size, 21);
    ASSERT_NEAR(optical_flow_config.max_movement_factor, 0.25, 1e-6);
    ASSERT_NEAR(optical_flow_config.cross_check_error_th, 0.5, 1e-6);
    ASSERT_NEAR(optical_flow_config.cross_check_error_rate_warning_threshold, 25.0, 1e-6);
    ASSERT_TRUE(optical_flow_config.reuse_input_image);
    ASSERT_FALSE(optical_flow_config.draw_optical_flow);
    ASSERT_EQ(optical_flow_config.optical_flow_output_path, "");
  }

  void TestSolverConfig(const Config& config) {
    const auto solver_config{config.GetSolverConfig()};

    ASSERT_EQ(solver_config.num_threads, 1);
    ASSERT_EQ(solver_config.max_num_iterations, 30);
    ASSERT_NEAR(solver_config.max_solver_time_in_seconds, 0.2, 1e-6);
    ASSERT_NEAR(solver_config.function_tolerance, 0.01, 1e-6);
  }

  void TestInitializerConfig(const Config& config, const size_t expected_min_matches = 50) {
    const auto initializer_config{config.GetInitializerConfig()};

    ASSERT_EQ(initializer_config.keyframe_num, 8);
    ASSERT_EQ(initializer_config.keyframe_gap, 5);
    ASSERT_EQ(initializer_config.min_landmarks, 30);
    ASSERT_TRUE(initializer_config.refine_imu);

    ASSERT_EQ(initializer_config.sfm_initializer_config.min_matches, expected_min_matches);
    ASSERT_NEAR(initializer_config.sfm_initializer_config.min_parallax, 10.0, 1e-6);
    ASSERT_EQ(initializer_config.sfm_initializer_config.min_triangulation, 50);
    ASSERT_EQ(initializer_config.sfm_initializer_config.random, 648);
  }

  void TestSWTConfig(const Config& config) {
    const auto sliding_window_tracker_config{config.GetSlidingWindowTrackerConfig()};

    ASSERT_EQ(sliding_window_tracker_config.min_mapped_landmark_count, 0);
    ASSERT_EQ(sliding_window_tracker_config.window_size, 10);
    ASSERT_EQ(sliding_window_tracker_config.subframe_size, 3);
    ASSERT_EQ(sliding_window_tracker_config.force_keyframe_landmarks, 35);
    ASSERT_NEAR(sliding_window_tracker_config.max_feature_height, 50, 1e-6);
    ASSERT_FALSE(sliding_window_tracker_config.parsac_flag);
    ASSERT_EQ(sliding_window_tracker_config.parsac_keyframe_check_size, 3);
    ASSERT_FALSE(sliding_window_tracker_config.draw_depth_points);
    ASSERT_EQ(sliding_window_tracker_config.depth_points_output_path, "");
  }

  void TestSensorConfig(const Config& config) {
    // Camera Config
    const auto camera_config{config.GetCameraConfig()};

    ASSERT_EQ(camera_config.intrinsics.type, spl::camera_models::CameraType::Pinhole);
    ASSERT_EQ(camera_config.intrinsics.distortion_type, spl::camera_models::DistortionType::RadTan);

    ASSERT_EQ(camera_config.intrinsics.pinhole_intrinsics.width, 752);
    ASSERT_EQ(camera_config.intrinsics.pinhole_intrinsics.height, 480);

    // WARN (VIPUL) Casting from float to double introduced floating point error so had to reduce the epsilon from 1e-6
    // to 1e-3
    ASSERT_NEAR(camera_config.K(0, 0), 458.654, 1e-3);  // fx
    ASSERT_NEAR(camera_config.K(0, 2), 367.215, 1e-3);  // cx
    ASSERT_NEAR(camera_config.K(1, 1), 457.296, 1e-3);  // fy
    ASSERT_NEAR(camera_config.K(1, 2), 248.375, 1e-3);  // cy
    ASSERT_EQ(camera_config.K(0, 1), 0.0);
    ASSERT_EQ(camera_config.K(1, 0), 0.0);
    ASSERT_EQ(camera_config.K(2, 0), 0.0);
    ASSERT_EQ(camera_config.K(2, 1), 0.0);
    ASSERT_EQ(camera_config.K(2, 2), 1.0);

    ASSERT_TRUE(camera_config.do_image_rectification);

    ASSERT_NEAR(camera_config.intrinsics.distortion.value().parameters[0], -0.28340811, 1e-6);
    ASSERT_NEAR(camera_config.intrinsics.distortion.value().parameters[1], 0.07395907, 1e-6);
    ASSERT_NEAR(camera_config.intrinsics.distortion.value().parameters[2], 0.00019359, 1e-6);
    ASSERT_NEAR(camera_config.intrinsics.distortion.value().parameters[3], 1.7618711e-05, 1e-6);

    ASSERT_EQ(camera_config.camera_tracking_margin, 20);

    ASSERT_TRUE(camera_config.camera_to_body_rotation.isApprox(Eigen::Quaterniond{
        7.1230146066895372e-01, -7.7071797555374275e-03, 1.0499323370587278e-02, 7.0175280029197162e-01}));
    ASSERT_TRUE(camera_config.camera_to_body_translation.isApprox(
        vector<3>(-0.0216401454975, -0.064676986768, 0.00981073058949), 1e-6));

    ASSERT_TRUE(camera_config.keypoint_noise_cov.isApprox(matrix<2>::Identity(), 1e-6));

    // Imu Config
    const auto imu_config{config.GetImuConfig()};

    ASSERT_TRUE(imu_config.imu_to_body_rotation.isApprox(Eigen::Quaterniond{1, 0, 0, 0}));
    ASSERT_TRUE(imu_config.imu_to_body_translation.isApprox(vector<3>::Zero(), 1e-6));

    ASSERT_TRUE(imu_config.gyroscope_noise_cov.isApprox(matrix<3>::Identity() * 2.8791302399999997e-8, 1e-6));
    ASSERT_TRUE(imu_config.accelerometer_noise_cov.isApprox(matrix<3>::Identity() * 4.0e-6, 1e-6));
    ASSERT_TRUE(imu_config.gyroscope_bias_noise_cov.isApprox(matrix<3>::Identity() * 3.7608844899999997e-10, 1e-6));
    ASSERT_TRUE(imu_config.accelerometer_bias_noise_cov.isApprox(matrix<3>::Identity() * 9.0e-6, 1e-6));
  }

  std::string sensor_config_path_;
};

TEST_F(DefaultConfigFixture, DefaultConfig) {
  const Config config{sensor_config_path_};

  TestFeatureConfig(config);
  TestSolverConfig(config);
  TestInitializerConfig(config);
  TestSWTConfig(config);
  TestSensorConfig(config);
}

TEST_F(DefaultConfigFixture, DefaultConfigWithFewParamsOverriden) {
  const auto sensor_config{utils::LoadFile(sensor_config_path_)};
  YAML::Node device_config;

  device_config["feature_tracker"]["max_frames"] = 10;
  device_config["initializer"]["sfm"]["min_matches"] = 0;

  const Config config{sensor_config, device_config};

  TestFeatureConfig(config, 10);
  TestSolverConfig(config);
  TestInitializerConfig(config, 0);
  TestSWTConfig(config);
  TestSensorConfig(config);
}