#include <gtest/gtest.h>

#include <rdvio/map/frame.h>
#include <rdvio/map/map.h>
#include <rdvio/map/track.h>
#include <rdvio/sliding_window_tracker.h>
#include <rdvio/testing_util/unit/construct_map_fixture.hpp>

using namespace rdvio;

TEST_F(ConstructMapFixture, TestTrackLandmarks) {
  Frame* const frame{map_->get_frame(0)};

  for (size_t k{1}; k < frame->keypoint_num(); ++k) {
    const Track* const track{frame->get_track(k)};
    EXPECT_FALSE(track->tag(TT_TRIANGULATED));
    EXPECT_FALSE(track->tag(TT_VALID));
    EXPECT_TRUE(track->tag(TT_STATIC));
  }

  TrackLandmarks(*frame);

  for (size_t k{1}; k < frame->keypoint_num(); ++k) {
    const Track* const track{frame->get_track(k)};
    EXPECT_TRUE(track->tag(TT_TRIANGULATED));
    EXPECT_TRUE(track->tag(TT_VALID));
    EXPECT_TRUE(track->tag(TT_STATIC));
  }
}