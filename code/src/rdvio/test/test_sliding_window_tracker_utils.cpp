#include <gtest/gtest.h>

#include <rdvio/sliding_window_tracker_utils.h>
#include <rdvio/testing_util/unit/construct_map_fixture.hpp>

using namespace rdvio;

TEST_F(ConstructMapFixture, ForwardTranslationOnly) {
  const Frame* const frame_0{map_->get_frame(0)};
  const Frame* const frame_1{map_->get_frame(1)};

  const Pose pose{ImuCameraPoseDelta(frame_0, frame_1)};

  EXPECT_TRUE(pose.q.toRotationMatrix().isApprox(matrix<3>::Identity()));
  EXPECT_TRUE(pose.p.isApprox(vector<3>{0, 0, -2}));
}