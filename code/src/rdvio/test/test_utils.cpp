#include <gtest/gtest.h>

#include "rdvio/util.hpp"

using namespace rdvio;

TEST(StatisticsLoggingTest, Enable) {
  // Redirect std::cout to a stringstream
  std::stringstream buffer;
  std::streambuf* old = std::cout.rdbuf(buffer.rdbuf());

  utils::LogStatistics("logger", "Working {} {}", 1, 2);

  std::cout.rdbuf(old);  // Restore original stream

#ifdef LOG_STATISTICS
  EXPECT_TRUE(buffer.str().find("[logger]: Working 1 2\n") != std::string::npos);
#else
  EXPECT_TRUE(buffer.str().find("[logger]: Working 1 2\n") == std::string::npos);
#endif
};
