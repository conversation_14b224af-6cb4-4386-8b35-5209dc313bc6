#include <gtest/gtest.h>

#include "rdvio/system_state_monitor.h"

using namespace rdvio;

class SystemStateMonitorTest : public ::testing::Test {
 protected:
  // cppcheck-suppress [unusedFunction, unmatchedSuppression]
  void SetUp() override { tracker_comp_ = monitor_.RegisterComponent("SWTLocalizer"); }

  // cppcheck-suppress [unusedFunction, unmatchedSuppression]
  void TearDown() override { monitor_.UnregisterComponent(tracker_comp_.get()); }

  SystemStateMonitor monitor_;
  // Make sure this destructed last!
  std::shared_ptr<ComponentHandle> tracker_comp_;
};

TEST_F(SystemStateMonitorTest, RegisterSingleComponentInitializesState) {
  EXPECT_EQ(monitor_.GetSystemState(), SystemState::INITIALIZING);
}

TEST_F(SystemStateMonitorTest, SingleComponentOKStateSystemOK) {
  tracker_comp_->ReportState(ComponentState::OK);
  EXPECT_EQ(monitor_.GetSystemState(), SystemState::TRACKING);
}

TEST_F(SystemStateMonitorTest, TestFailureEstimation) {
  const auto frontend_comp{monitor_.RegisterComponent("SlidingWindowTracker")};

  tracker_comp_->ReportState(ComponentState::CRITICAL);
  frontend_comp->ReportState(ComponentState::CRITICAL);
  EXPECT_EQ(monitor_.GetSystemState(), SystemState::CRASHED);
}
