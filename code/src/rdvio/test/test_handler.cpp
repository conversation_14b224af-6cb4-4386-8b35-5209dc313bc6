#include <gtest/gtest.h>

#include <rdvio/rdvio.hpp>

using namespace rdvio;

class HandlerFixture : public ::testing::Test {
 protected:
  // cppcheck-suppress [unusedFunction, unmatchedSuppression]
  void SetUp() override {
    const std::string calib_file{"/testroot/code/test/test_data/euroc/euroc.yaml"};
    const std::string config_file{"/testroot/code/test/test_data/euroc/setting.yaml"};

    const auto config{std::make_shared<Config>(calib_file, config_file)};
    handler_ = std::make_unique<Handler>(config);
  }
  // cppcheck-suppress [unusedFunction, unmatchedSuppression]
  void TearDown() override { handler_->Exit(); }

  std::unique_ptr<Handler> handler_;
  // cppcheck-suppress [unusedStructMember]
  const std::vector<vector<3>> accelerometer_data_{vector<3>{1, 1, 1}, vector<3>{2, 2, 2}, vector<3>{3, 3, 3}};
  // cppcheck-suppress [unusedStructMember]
  const std::vector<vector<3>> gyroscope_data_{vector<3>{4, 4, 4}, vector<3>{5, 5, 5}, vector<3>{6, 6, 6}};
};

TEST_F(HandlerFixture, AddMotionTogether) {
  for (size_t i = 0; i < 3; i++) {
    handler_->TrackImu({static_cast<double>(i), gyroscope_data_[i], accelerometer_data_[i]});
  }

  const auto imus_data{handler_->GetImuQueue()};

  for (size_t i = 0; i < 3; i++) {
    ASSERT_NEAR(i, imus_data[i].t, 1e-6);
    ASSERT_TRUE(accelerometer_data_[i].isApprox(imus_data[i].a, 1e-6));
    ASSERT_TRUE(gyroscope_data_[i].isApprox(imus_data[i].w, 1e-6));
  }
}

TEST_F(HandlerFixture, PropogateState) {
  double state_time{1};
  PoseState state_pose{matrix<3>::Identity(), vector<3>{0, 0, 0}};  // Origin
  MotionState state_motion;
  state_motion.linear_velocity = vector<3>{1, 0, 0};  // 1 m/s along x-axis

  const double imu_timestamp{2};
  const vector<3> imu_angular_velocity{1, 0, 0};                   // Rotaion around x-axis
  const vector<3> imu_linear_acceleration{1, 0, kNominalGravity};  // 1 m/s^2 along x-axis

  handler_->PropagateState(state_time, imu_timestamp, imu_angular_velocity, imu_linear_acceleration, state_pose,
                           state_motion);

  const Eigen::Quaterniond expected_quaternion(0.877583, 0.479426, 0, 0);
  ASSERT_NEAR(state_time, imu_timestamp, 1e-6);
  ASSERT_TRUE(state_pose.p.isApprox(vector<3>{1.5, 0, 0}, 1e-6));
  ASSERT_TRUE(state_pose.q.isApprox(expected_quaternion, 1e-3));
  ASSERT_TRUE(state_motion.linear_velocity.isApprox(vector<3>{2, 0, 0}, 1e-6));
}
