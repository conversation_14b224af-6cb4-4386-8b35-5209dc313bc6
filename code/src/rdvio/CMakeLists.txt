# rdvio core
target_sources(${PROJECT_NAME}
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src/handler.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/src/feature_tracker.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/src/frontend.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/src/sliding_window_tracker.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/src/config.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/src/types.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/src/system_state_monitor.cpp
)

target_include_directories(${PROJECT_NAME}
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
        $<INSTALL_INTERFACE:include>
)

if (BUILD_TESTING)
    find_package(GTest REQUIRED)

    set(TEST_LIST
            test_config
            test_default_config
            test_system_state_monitor
            test_handler
            test_sliding_window_tracker
            test_sliding_window_tracker_utils
            test_utils
    )

    foreach (TEST_NAME ${TEST_LIST})
        set(EXEC_NAME ${TEST_NAME})

        add_executable(${EXEC_NAME})
        target_sources(${EXEC_NAME} PRIVATE
                "test/${EXEC_NAME}.cpp")
        target_link_libraries(${EXEC_NAME} PRIVATE
                ${PROJECT_NAME}
                ${GTEST_LIBRARIES}
                gtest_main
        )
        target_include_directories(${EXEC_NAME} PRIVATE
                ${GTEST_INCLUDE_DIRS}
        )
        add_test(
                NAME ${EXEC_NAME}
                COMMAND $<TARGET_FILE:${EXEC_NAME}>
                WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        )
    endforeach (TEST_NAME)
endif (BUILD_TESTING)
