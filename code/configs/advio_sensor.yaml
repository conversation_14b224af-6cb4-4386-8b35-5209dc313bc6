# seq 1-12
imu:
  # inertial sensor noise model parameters (static)
  gyroscope_noise_density: 0.0024       # [ rad / s / sqrt(Hz) ]
  gyroscope_random_walk: 0.000051       # [ rad / s^2 / sqrt(Hz) ]
  accelerometer_noise_density: 0.0048    # [ m / s^2 / sqrt(Hz) ]
  accelerometer_random_walk: 0.00021    # [ m / s^3 / sqrt(Hz) ]

cam0:
  do_image_rectification: true
  resolution: [720, 1280]        # resolution of camera
  camera_model: pinhole         # camera model
  distortion_model: radtan      # distortion model
  intrinsics: [1077.2, 1079.3, 362.14, 636.39] # fu, fv, cu, cv
  distortion_coeffs: [-0.0003, -0.0009, 0.0478, 0.0339] # k1, k2, p1, p2
  extrinsic:
    q_bc: [ -0.0020364, -0.002772, -0.0011561, 0.9999934  ] # x y z w
    p_bc: [ -0.008977668364731128, 0.07557012320238939, -0.005545773942541918 ] # x y z [m]
  noise: [
    0.5, 0.0,
    0.0, 0.5] # [pixel^2]
