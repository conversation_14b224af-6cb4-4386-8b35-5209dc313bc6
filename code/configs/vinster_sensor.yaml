imu:
  # inertial sensor noise model parameters (static)
  gyroscope_noise_density: 0.0001519267843228526      # [rad / s / sqrt(Hz)]
  gyroscope_random_walk:   4.2190233578228e-06        # [rad / s^2 / sqrt(Hz)]
  accelerometer_noise_density: 0.002174873717654205   # [m / s^2 / sqrt(Hz)]
  accelerometer_random_walk:   9.520536025976413e-05  # [m / s^3 / sqrt(Hz)]

cam0:
  do_image_rectification: true
  resolution: [960, 540]        # resolution of camera
  camera_model: pinhole         # camera model
  distortion_model: radtan      # distortion model
  intrinsics: [681.4498676697148, 687.8857649507729, 452.32131525432146, 247.55469181997395] # fu, fv, cu, cv
  ds_intrinsics: [0.0, 0.0]     # xi, alpha
  distortion_coeffs: [-0.3102632629207263, 0.0887827723405766, -0.0007965134651555057, -0.0007319696281739201] # k1, k2, p1, p2
  extrinsic:
    q_bc: [ 0, 0, 0, 1.0] # x y z w
    p_bc: [ -0.0, -0.0, 0.00 ] # x y z [m]
  noise: [
    0.5, 0.0,
    0.0, 0.5] # [pixel^2]
