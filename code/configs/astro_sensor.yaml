imu:
  # inertial sensor noise model parameters (static)
  gyroscope_noise_density: 0.0020074246247851006      # [rad / s / sqrt(Hz)]
  gyroscope_random_walk:   8.480974251286652e-05        # [rad / s^2 / sqrt(Hz)]
  accelerometer_noise_density: 0.023793611597588836   # [m / s^2 / sqrt(Hz)]
  accelerometer_random_walk:   0.004643759094432462  # [m / s^3 / sqrt(Hz)]

cam0:
  do_image_rectification: true
  resolution: [1920, 1080]        # resolution of camera
  camera_model: pinhole         # camera model
  distortion_model: radtan      # distortion model
  intrinsics: [1252.9254297187415, 1256.07779029245, 982.6869694332239, 546.2475441521397] # fu, fv, cu, cv
  ds_intrinsics: [-0.08321483586627619, 0.10524094663632039]     # xi, alpha
  distortion_coeffs: [0.0, 0.0, 0.0, 0.0] # k1, k2, p1, p2
  extrinsic:
    # q_bc: [ -0.5, 0.5, -0.5, 0.5] # x y z w
    q_bc: [ -0.4055798, 0.4055798, -0.579228, 0.579228] # x y z w
    p_bc: [ 0.0435, 0.018, 0.021 ] # x y z [m]
  noise: [
    0.5, 0.0,
    0.0, 0.5] # [pixel^2]
