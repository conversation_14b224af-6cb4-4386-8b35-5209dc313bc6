# generated by generate_build_pipeline.py
code_checks:
  clang_format:
    type: container
    src: .
  cpp_check:
    type: container
    std: c++17
    suppress: missingInclude,missingIncludeSystem
    src: .
builds:
  - name: spl-rd-vio-cuda-12.1-trt-8.6.1-jammy-amd64
    enabled: 1
    folder: .
    build_folder: .
    order: 1
    agents:
      cuda:
        prebuild_script: building/pipeline_scripts/replace_version_and_revision.sh
        base_image: spleenlab/spl-rd-vio/amd64-base:3.0.1
        docker_stage: test-stage
        file: building/Dockerfile
        platform: linux/amd64
        postbuild:
          - platform: linux/amd64
            stage: build-stage
            artefacts:
              - name: spl-rd-vio-cuda-12.1-trt-8.6.1
                enabled: 1
                type: apt
                codename: jammy
                path: /buildroot/debian-files/spl-rd-vio-cuda-12.1-trt-8.6.1_<artefact_version>-<artefact_revision>_amd64.deb
              - name: spl-rd-vio-cuda-12.1-trt-8.6.1-dev
                enabled: 1
                type: apt
                codename: jammy
                path: /buildroot/debian-files/spl-rd-vio-cuda-12.1-trt-8.6.1-dev_<artefact_version>-<artefact_revision>_amd64.deb
        build_args:
          - key: SPL_LIBRARY_NAME
            value: spl-rd-vio-cuda-12.1-trt-8.6.1
          - key: BUILD_TYPE_DEV
            value: RelWithDebInfo
          - key: BUILD_TYPE_RELEASE
            value: Release
          - key: SPL_INSTALL_PREFIX
            value: /opt/spl-packages
    type: Dockerfile
    configuration:
      build_development: 1
      test:
        script: building/pipeline_scripts/run_library_integration.sh
        stage: test-stage
        platform: linux/amd64


  - name: spl-rd-vio-l4t-r36.4.0-jammy-arm64
    enabled: 1
    folder: .
    build_folder: .
    order: 2
    agents:
      l4t-r36.4.0:
        prebuild_script: building/pipeline_scripts/replace_version_and_revision.sh
        base_image: spleenlab/spl-rd-vio/l4t-r36.4.0-base:3.0.1
        docker_stage: test-stage
        file: building/Dockerfile
        platform: linux/arm64
        postbuild:
          - platform: linux/arm64
            stage: build-stage
            artefacts:
              - name: spl-rd-vio-l4t-r36.4.0
                enabled: 1
                type: apt
                codename: jammy
                path: /buildroot/debian-files/spl-rd-vio-l4t-r36.4.0_<artefact_version>-<artefact_revision>_arm64.deb
              - name: spl-rd-vio-l4t-r36.4.0-dev
                enabled: 1
                type: apt
                codename: jammy
                path: /buildroot/debian-files/spl-rd-vio-l4t-r36.4.0-dev_<artefact_version>-<artefact_revision>_arm64.deb
        build_args:
          - key: SPL_LIBRARY_NAME
            value: spl-rd-vio-l4t-r36.4.0
          - key: BUILD_TYPE_DEV
            value: RelWithDebInfo
          - key: BUILD_TYPE_RELEASE
            value: Release
          - key: SPL_INSTALL_PREFIX
            value: /opt/spl-packages
    type: Dockerfile
  - name: spl-rd-vio-jammy-arm64
    enabled: 1
    folder: .
    build_folder: .
    order: 3
    agents:
      arm64:
        prebuild_script: building/pipeline_scripts/replace_version_and_revision.sh
        base_image: spleenlab/spl-rd-vio/arm64-base:3.0.1
        docker_stage: test-stage
        file: building/Dockerfile
        platform: linux/arm64
        postbuild:
          - platform: linux/arm64
            stage: build-stage
            artefacts:
              - name: spl-rd-vio
                enabled: 1
                type: apt
                codename: jammy
                path: /buildroot/debian-files/spl-rd-vio_<artefact_version>-<artefact_revision>_arm64.deb
              - name: spl-rd-vio-dev
                enabled: 1
                type: apt
                codename: jammy
                path: /buildroot/debian-files/spl-rd-vio-dev_<artefact_version>-<artefact_revision>_arm64.deb
        build_args:
          - key: SPL_LIBRARY_NAME
            value: spl-rd-vio
          - key: BUILD_TYPE_DEV
            value: RelWithDebInfo
          - key: BUILD_TYPE_RELEASE
            value: Release
          - key: SPL_INSTALL_PREFIX
            value: /opt/spl-packages
    type: Dockerfile
artefact:
  repo_path: spleenlab/spl-rd-vio # target folder documentation/scans in raw repository
documentation:
  doxygen:
    enabled: 1
