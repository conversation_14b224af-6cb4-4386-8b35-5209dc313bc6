PROJECT_NAME = "SPL RD VIO"
INPUT = ./README.md code
FILE_PATTERNS = *.h*
EXCLUDE_PATTERNS = */spl-build-pipeline/* */cmake-build-debug/*
OUTPUT_DIRECTORY = doxygen/spl-rd-vio

# Customize default behaviour
PROJECT_LOGO = /resource/cropped-logo_bg_transparent_neg-1-300x86.png
HTML_HEADER = /resource/header.html
HTML_EXTRA_STYLESHEET = /resource/doxygen-awesome-css/doxygen-awesome.css \
                        /resource/doxygen-awesome-css/doxygen-awesome-sidebar-only.css \
                        /resource/doxygen-spleenlab.css
HTML_EXTRA_FILES = /resource/doxygen-awesome-css/doxygen-awesome-fragment-copy-button.js \
                   /resource/doxygen-awesome-css/doxygen-awesome-paragraph-link.js \
                   /resource/cropped-social-logo-trans-neg-32x32.png
PLANTUML_JAR_PATH = /resource/plantuml.jar
HTML_COLORSTYLE = LIGHT
GENERATE_TREEVIEW = YES
DISABLE_INDEX = NO
FULL_SIDEBAR = NO
DOXYFILE_ENCODING = UTF-8
ALLOW_UNICODE_NAMES = YES
OUTPUT_LANGUAGE = English
INHERIT_DOCS = YES
MARKDOWN_SUPPORT = YES
EXTRACT_ALL = YES
EXTRACT_PRIVATE = YES
EXTRACT_PACKAGE = YES
EXTRACT_STATIC = YES
EXTRACT_LOCAL_CLASSES = YES
GENERATE_LATEX = YES
RECURSIVE = YES
CLASS_DIAGRAMS = YES
HIDE_UNDOC_RELATIONS = YES
HAVE_DOT=YES
UML_LOOK=YES
CLASS_GRAPH = YES
COLLABORATION_GRAPH = YES
TEMPLATE_RELATIONS = YES
DOT_GRAPH_MAX_NODES = 100
MAX_DOT_GRAPH_DEPTH = 0
DOT_TRANSPARENT = YES
USE_MDFILE_AS_MAINPAGE = ./README.md
