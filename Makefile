build-dev-spl-rd-vio:
	cd building/local && ./build_image.sh

test-spl-rd-vio:
	cd building/local && ./build_image.sh test

build-dev-spl-rd-vio-arm64:
	cd building/local && ./build_image.sh --build-arg "BASE_IMAGE=docker.int.spleenlab.ai:30004/spleenlab/spl-rd-vio/arm64-base:2.2.0"

build-spl-rd-vio-arm64:
	cd building/local && ./build_image.sh build --build-arg "BASE_IMAGE=docker.int.spleenlab.ai:30004/spleenlab/spl-rd-vio/arm64-base:2.2.0"


test-spl-rd-vio-arm64:
	cd building/local && ./build_image.sh test --build-arg "BASE_IMAGE=docker.int.spleenlab.ai:30004/spleenlab/spl-rd-vio/arm64-base:2.2.0"

generate-build-pipeline:
	python3 pipeline/generate_build_pipeline.py --output_path pipeline --output_file build-pipeline.yml
