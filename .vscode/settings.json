{"files.associations": {"*.ipp": "cpp", "array": "cpp", "deque": "cpp", "forward_list": "cpp", "list": "cpp", "string": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "string_view": "cpp", "initializer_list": "cpp", "__bit_reference": "cpp", "__hash_table": "cpp", "__split_buffer": "cpp", "__tree": "cpp", "map": "cpp", "set": "cpp", "cctype": "cpp", "cmath": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "atomic": "cpp", "bit": "cpp", "*.tcc": "cpp", "bitset": "cpp", "charconv": "cpp", "chrono": "cpp", "clocale": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "condition_variable": "cpp", "cstdint": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "ostream": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "thread": "cpp", "typeinfo": "cpp", "variant": "cpp", "__locale": "cpp", "__node_handle": "cpp", "__verbose_abort": "cpp", "execution": "cpp", "ios": "cpp", "locale": "cpp", "print": "cpp", "queue": "cpp"}}