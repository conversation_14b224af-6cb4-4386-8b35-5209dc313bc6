ARG BASE_IMAGE=docker.int.spleenlab.ai:30004/spleenlab/spl-rd-vio/amd64-base:3.0.1
FROM $BASE_IMAGE AS configuration-stage

ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8
ENV DEBIAN_FRONTEND=noninteractive
ENV SHELL=/bin/bash
SHELL ["/bin/bash", "-c"]
WORKDIR /buildroot

ARG SPL_NETWORK=local
ENV SPL_NETWORK=$SPL_NETWORK
# See documentation on the available options and there meaning here:
#       http://gitlab.int.spleenlab.ai:20080/spleenlab/wiki/-/wikis/Build_Pipeline#spl_repository-variable
ARG SPL_REPOSITORY=releases
ENV SPL_REPOSITORY=$SPL_REPOSITORY
ARG NUM_THREADS=8
ARG NUM_THREADS=$NUM_THREADS

ARG INSTALL_CONFIGURATION_DEPENDENCIES=building/install_scripts/install_configuration_dependencies.sh
RUN --mount=type=bind,source=$INSTALL_CONFIGURATION_DEPENDENCIES,target=/temporary/$INSTALL_CONFIGURATION_DEPENDENCIES \
    /temporary/$INSTALL_CONFIGURATION_DEPENDENCIES

RUN set -eoux pipefail; \
    rm --force /etc/localtime; \
    echo 'Etc/UTC' > /etc/timezone; \
    ln --symbolic /usr/share/zoneinfo/Etc/UTC /etc/localtime


FROM ubuntu:22.04 AS reference-data-download-stage

ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8
ENV DEBIAN_FRONTEND=noninteractive
ENV SHELL=/bin/bash
SHELL ["/bin/bash", "-c"]
WORKDIR /buildroot

ARG DOWNLOAD_REFERENCE_DATA=building/scripts/download_reference_data.sh
RUN --mount=type=bind,source=$DOWNLOAD_REFERENCE_DATA,target=/temporary/$DOWNLOAD_REFERENCE_DATA \
    --mount=type=secret,id=netrc,uid=1000,dst=/root/.netrc \
      set -eoux pipefail; \
      apt-get update; apt-get install --yes curl unzip zip; \
      source /temporary/$DOWNLOAD_REFERENCE_DATA; \
      download_reference_zip_data http://nexus.int.spleenlab.ai:30001/repository/test-data/spl_vio/test_scenarios/MH_01_easy/zip/MH_01_easy.zip MH_01_easy.zip; \
      download_reference_data http://nexus.int.spleenlab.ai:30001/repository/test-data/spl_vio/test_scenarios/MH_01_easy/eval/MH_01_easy.txt

FROM configuration-stage AS base-stage

ARG INSTALL_BUILD_DEPENDENCIES=building/install_scripts/install_build_dependencies.sh
ARG UTILS=building/install_scripts/utils.sh
RUN --mount=type=bind,source=$INSTALL_BUILD_DEPENDENCIES,target=/temporary/$INSTALL_BUILD_DEPENDENCIES \
    --mount=type=bind,source=$UTILS,target=/temporary/$UTILS \
    --mount=type=secret,id=aptauth,uid=1000,dst=/etc/apt/auth.conf \
    --mount=type=secret,id=aptconf,uid=1000,dst=/etc/apt/apt.conf \
      /temporary/$INSTALL_BUILD_DEPENDENCIES

ARG INSTALL_LIBRARY_DEPENDENCIES=building/install_scripts/install_library_dependencies.sh
RUN --mount=type=bind,source=$INSTALL_LIBRARY_DEPENDENCIES,target=/temporary/$INSTALL_LIBRARY_DEPENDENCIES \
    --mount=type=bind,source=$UTILS,target=/temporary/$UTILS \
    --mount=type=secret,id=aptauth,uid=1000,dst=/etc/apt/auth.conf \
    --mount=type=secret,id=aptconf,uid=1000,dst=/etc/apt/apt.conf \
      /temporary/$INSTALL_LIBRARY_DEPENDENCIES

ARG INSTALL_SPL_CAMERA_MODELS=building/install_scripts/install_spl_camera_models_from_source.sh
RUN --mount=type=bind,source=$INSTALL_SPL_CAMERA_MODELS,target=/temporary/$INSTALL_SPL_CAMERA_MODELS \
    --mount=type=bind,source=$UTILS,target=/temporary/$UTILS \
    --mount=type=secret,id=aptauth,uid=1000,dst=/etc/apt/auth.conf \
    --mount=type=secret,id=aptconf,uid=1000,dst=/etc/apt/apt.conf \
    --mount=type=secret,id=gitlabtoken,uid=1000,dst=/root/gitlabtoken \
      /temporary/$INSTALL_SPL_CAMERA_MODELS

FROM base-stage as test-base-stage

ARG UTILS=building/install_scripts/utils.sh
ARG INSTALL_INTEGRATION_TEST_DEPENDENCIES=building/install_scripts/install_integration_test_dependencies.sh
RUN --mount=type=bind,source=$INSTALL_INTEGRATION_TEST_DEPENDENCIES,target=/temporary/$INSTALL_INTEGRATION_TEST_DEPENDENCIES \
    --mount=type=bind,source=$UTILS,target=/temporary/$UTILS \
    --mount=type=secret,id=aptauth,uid=1000,dst=/etc/apt/auth.conf \
    --mount=type=secret,id=aptconf,uid=1000,dst=/etc/apt/apt.conf \
      /temporary/$INSTALL_INTEGRATION_TEST_DEPENDENCIES


FROM base-stage AS build-stage

COPY --from=reference-data-download-stage /testroot/data/ /testroot/data/
COPY ./code/test/test_data/ /testroot/code/test/test_data/

ARG BUILD_TESTING=ON
ARG BUILD_TYPE_DEV=RelWithDebInfo
ARG BUILD_TYPE_RELEASE=Release
ARG MULTITHREADING=ON
ARG PACKAGE_LIBRARY=ON
ARG LOG_STATISTICS=OFF
ARG SPL_LIBRARY_NAME
ENV SPL_LIBRARY_NAME=$SPL_LIBRARY_NAME
ENV SPL_INSTALL_PREFIX=/opt/spl-packages
ARG SPL_LOGGING_LEVEL=info
ENV SPL_LOGGING_LEVEL=$SPL_LOGGING_LEVEL

RUN  --mount=type=bind,source=./code,target=/temporary/code \
     --mount=type=bind,source=./building/scripts/build_library.sh,target=/temporary/building/scripts/build_library.sh \
     --mount=type=bind,source=./building/scripts/parse_package_xml.sh,target=/temporary/building/scripts/parse_package_xml.sh \
     --mount=type=bind,source=./package.xml,target=/temporary/package.xml \
      source /temporary/building/scripts/parse_package_xml.sh; \
        /temporary/building/scripts/build_library.sh $BUILD_TYPE_RELEASE $BUILD_TESTING $PACKAGE_LIBRARY $MULTITHREADING $LOG_STATISTICS; \
      source /temporary/building/scripts/parse_package_xml.sh; \
        /temporary/building/scripts/build_library.sh $BUILD_TYPE_DEV $BUILD_TESTING $PACKAGE_LIBRARY $MULTITHREADING $LOG_STATISTICS


FROM test-base-stage AS test-stage

COPY --from=reference-data-download-stage /testroot/data/ /testroot/data/
COPY ./code/test/test_data/ /testroot/code/test/test_data/

ARG BUILD_TESTING=ON
ARG BUILD_TYPE_RELEASE=Release
ARG MULTITHREADING=OFF
ARG PACKAGE_LIBRARY=OFF
ARG LOG_STATISTICS=ON
ARG SPL_LIBRARY_NAME
ENV SPL_LIBRARY_NAME=$SPL_LIBRARY_NAME
ENV SPL_INSTALL_PREFIX=/opt/spl-packages

RUN  --mount=type=bind,source=./code,target=/temporary/code \
     --mount=type=bind,source=./building/scripts/build_library.sh,target=/temporary/building/scripts/build_library.sh \
     --mount=type=bind,source=./building/scripts/parse_package_xml.sh,target=/temporary/building/scripts/parse_package_xml.sh \
     --mount=type=bind,source=./package.xml,target=/temporary/package.xml \
      source /temporary/building/scripts/parse_package_xml.sh; \
        /temporary/building/scripts/build_library.sh $BUILD_TYPE_RELEASE $BUILD_TESTING $PACKAGE_LIBRARY $MULTITHREADING $LOG_STATISTICS

ARG RUN_TESTING_BATTERY=building/scripts/run_testing_battery.sh
RUN --mount=type=bind,source=$RUN_TESTING_BATTERY,target=/temporary/$RUN_TESTING_BATTERY \
    /temporary/$RUN_TESTING_BATTERY

ARG RUN_TEST_METRICS=building/scripts/run_test_metrics.py
RUN --mount=type=bind,source=$RUN_TEST_METRICS,target=/temporary/$RUN_TEST_METRICS \
    set -eoux pipefail; \
    source /buildroot/env/bin/activate; \
    export PATH=/root/.local/bin:$PATH; \
      python3 /temporary/$RUN_TEST_METRICS


FROM base-stage AS development-stage

COPY --from=reference-data-download-stage /testroot/data/ /testroot/data/
# If you want access to the configs required for the testing you should mount them to the container!
#      --volume <your_abs_path>/code/test/test_data/ /testroot/code/test/test_data/
# We could have copied them in, but that would mean during development you could not edit them without rebuilding the
# image. There I prefer that the tests fail and the user has to mount them into the development environment, instead of
# being consfused why their changes in the config file do not show up in the code!

ARG INSTALL_DEVELOPMENT_DEPENDENCIES=building/install_scripts/install_development_dependencies.sh
RUN --mount=type=bind,source=$INSTALL_DEVELOPMENT_DEPENDENCIES,target=/temporary/$INSTALL_DEVELOPMENT_DEPENDENCIES \
    --mount=type=bind,source=$UTILS,target=/temporary/$UTILS \
    --mount=type=secret,id=aptauth,uid=1000,dst=/etc/apt/auth.conf \
    --mount=type=secret,id=aptconf,uid=1000,dst=/etc/apt/apt.conf \
    /temporary/$INSTALL_DEVELOPMENT_DEPENDENCIES



