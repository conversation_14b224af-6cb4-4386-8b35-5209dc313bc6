#!/bin/bash

########## WARN #########
#
# THIS FILE IS NOT USED BY THE BUILD PIPELINE, INTENDED FOR LOCAL EXECUTION ONLY
#
########## WARN #########

set -eo pipefail

if [ -z "$1" ];
then
  echo "No STAGE argument supplied, defaulting to 'development'."
  STAGE=development
  declare -a ADDITIONAL_OPTIONS=()
else
  STAGE="${1}"
  declare -a ADDITIONAL_OPTIONS=("${@:2}")
fi

set -eou pipefail

LIB_NAME=spl-rd-vio
TARGET_IMAGE_NAME="${LIB_NAME}:${STAGE}"
TARGET_STAGE="${STAGE}-stage"
echo "Building ${TARGET_IMAGE_NAME}"

SPL_NETWORK=$(if ip address | grep --quiet '10.1.'; then echo 'local'; else echo 'vpn'; fi)

SCRIPT_PATH="$(dirname "$(readlink --canonicalize "${0}")")"
docker build "${SCRIPT_PATH}/../../" \
  --build-arg "SPL_NETWORK=${SPL_NETWORK}" \
  --build-arg "SPL_LIBRARY_NAME=spl-rd-vio" \
  --file "${SCRIPT_PATH}/../Dockerfile"  \
  --progress=plain \
  --secret "id=aptauth,src=/etc/apt/auth.conf" \
  --secret "id=aptconf,src=/etc/apt/apt.conf" \
  --secret "id=netrc,src=${HOME}/.netrc" \
  --secret "id=gitlabtoken,src=${HOME}/.gitlabtoken" \
  --tag "${TARGET_IMAGE_NAME}" \
  --target "${TARGET_STAGE}" \
  "${ADDITIONAL_OPTIONS[@]}"