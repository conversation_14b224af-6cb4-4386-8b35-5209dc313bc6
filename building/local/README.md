# Local Docker Scripts

## Building the image

        # builds requested stage - the script automatically appends `-stage`
        ./build_image.sh build

        # builds the development stage by default when no argument is provided
        ./build_image.sh

## Running a container image

Containers launched using this script will not have the local source code mounted onto them and are not intended for
core development! Ask your team lead how to set up the Docker development environment with your IDE if you want to do
core development.

        # runs the requested container stage image
        ./run_image.sh build

        # runs the development container stage image by default when no argument is provided
        ./run_image.sh 

## Gitlab Token

In your local home directory you need a file named `.gitlabtoken`. In this file you should have the following:

    user_name:<gitlab_token_with_read_api_access>

For example mine looks something like:

    jborer:1234567890ßqwertzuier

You can create a token with `read_api` access using the gitlab GUI in the settings page. If you have questions google it 
first, then ask jack :)