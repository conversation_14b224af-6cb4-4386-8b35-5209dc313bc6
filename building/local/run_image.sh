#!/bin/bash

########## WARN #########
#
# THIS FILE IS NOT USED BY THE BUILD PIPELINE, INTENDED FOR LOCAL EXECUTION ONLY
#
########## WARN #########

set -eo pipefail

if [ -z "$1" ];
then
  echo "No STAGE argument supplied, defaulting to 'development'."
  STAGE=development
else
  STAGE="$1"
fi

set -eou pipefail

LIB_NAME=spl-rd-vio
TARGET_IMAGE="${LIB_NAME}:${STAGE}"

echo "Running ${TARGET_IMAGE}"

docker run \
  --entrypoint /bin/bash \
  --interactive \
  --name "${LIB_NAME}" \
  --network host \
  --rm \
  --volume /media/aabouee/data/images/quantum/new_gilching_11_11/rosbag2_2025-08-07_11-11-13_0/:/dataset \
  --tty \
  "${TARGET_IMAGE}"
