#!/bin/bash

download_reference_zip_data(){
    local URL="${1}"
    local DATA_DIR="/testroot/data/"

    mkdir --parents "${DATA_DIR}"
    cd "${DATA_DIR}" || exit

    curl --fail --netrc --remote-name "${URL}"

    local FILE_NAME="${2}"  
    unzip -q "${FILE_NAME}"
    rm "${FILE_NAME}"
}

download_reference_data(){
    local URL="${1}"
    local DATA_DIR="/testroot/data/"

    mkdir --parents "${DATA_DIR}"
    cd "${DATA_DIR}" || exit

    curl --fail --netrc --remote-name "${URL}"
}

