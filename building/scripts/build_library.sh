#!/bin/bash

set -eox pipefail

if [ -z "$1" ];
then
  echo "No BUILD_TYPE argument supplied, defaulting to 'RelWithDebInfo'."
  BUILD_TYPE=RelWithDebInfo
else
  BUILD_TYPE="$1"
fi


if [ -z "$2" ];
then
  echo "No BUILD_TESTING argument supplied, defaulting to 'ON'."
  BUILD_TESTING=ON
else
  BUILD_TESTING="$2"
fi

if [ -z "$3" ];
then
  echo "No PACKAGE_LIBRARY argument supplied, defaulting to 'ON'."
  PACKAGE_LIBRARY=ON
else
  PACKAGE_LIBRARY="$3"
fi

if [ -z "$4" ];
then
  echo "No ENABLE_MULTITHREADING argument supplied, defaulting to 'ON'."
  ENABLE_MULTITHREADING=ON
else
  ENABLE_MULTITHREADING="$4"
fi

if [ -z "$5" ];
then
  echo "No LOG_STATISTICS argument supplied, defaulting to 'OFF'."
  LOG_STATISTICS=OFF
else
  LOG_STATISTICS="$5"
fi

set -eoux pipefail

rm --recursive --force /buildroot/build-"${BUILD_TYPE}"; mkdir --parents /buildroot/build-"${BUILD_TYPE}"; cd /buildroot/build-"${BUILD_TYPE}"

ldconfig

cmake /temporary/code -G Ninja \
  -DBUILD_TESTING="${BUILD_TESTING}" \
  -DCMAKE_BUILD_TYPE="${BUILD_TYPE}" \
  -DCMAKE_INSTALL_PREFIX="${SPL_INSTALL_PREFIX}" \
  -DCMAKE_MODULE_PATH="${SPL_INSTALL_PREFIX}" \
  -DLIB_VERSION="${SPL_LIBRARY_VERSION}" \
  -DPACKAGE_LIBRARY="${PACKAGE_LIBRARY}" \
  -DTHREADING="${ENABLE_MULTITHREADING}" \
  -DLOG_STATISTICS="${LOG_STATISTICS}"
time cmake --build . --parallel "${NUM_THREADS}"

if [[ "${BUILD_TESTING}" == ON ]]; then
  ctest --output-on-failure
fi

if [[ "${PACKAGE_LIBRARY}" == ON ]]; then
  if [[ "${BUILD_TYPE}" == "Release" ]]; then
    COMPONENTS="Runtime"
    DEBIAN_PACKAGE_NAME="${SPL_LIBRARY_NAME}"
  else
    COMPONENTS="SourceCode;Runtime"
    DEBIAN_PACKAGE_NAME="${SPL_LIBRARY_NAME}-dev"
  fi

  cpack -G DEB \
    -D CPACK_COMPONENTS_ALL="${COMPONENTS}" \
    -D CPACK_DEBIAN_PACKAGE_NAME="${DEBIAN_PACKAGE_NAME}" \
    -D CPACK_DEBIAN_PACKAGE_RELEASE="${SPL_LIBRARY_REVISION}" \
    -D CPACK_DEBIAN_PACKAGE_VERSION="${SPL_LIBRARY_VERSION}" \
    -D CPACK_PACKAGE_CONTACT="${SPL_LIBRARY_EMAIL}" \
    -D CPACK_PACKAGE_DESCRIPTION_SUMMARY="${SPL_LIBRARY_DESCRIPTION}" \


  mkdir --parents /buildroot/debian-files
  cp ./*.deb /buildroot/debian-files
fi

