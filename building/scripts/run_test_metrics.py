import evo.main_rpe as main_rpe
from evo.core import sync
from evo.core.metrics import PoseRelation
from evo.core.units import Unit
from evo.tools import file_interface
import os


def load_all_txt_files(directory):
    all_files = []
    for file in os.listdir(directory):
        if file.endswith(".txt"):
            all_files.append(os.path.join(directory, file))

    return all_files


def evaluate_trajectory(reference_trajectory_file, estimated_trajectory_file):
    traj_ref = file_interface.read_tum_trajectory_file(reference_trajectory_file)
    traj_est = file_interface.read_tum_trajectory_file(estimated_trajectory_file)
    traj_ref, traj_est = sync.associate_trajectories(traj_ref, traj_est)

    result = main_rpe.rpe(traj_ref, traj_est, pose_relation=PoseRelation.full_transformation, delta=1.0,
                          delta_unit=Unit.meters)

    path_length = traj_est.path_length
    assert 3.2 < path_length < 3.4, "path_length: {}".format(path_length)
    rmse = result.stats["rmse"]
    assert 0.8 < rmse < 1.0, "rmse: {}".format(rmse)
    mean = result.stats["mean"]
    assert 0.7 < mean < 0.9, "mean: {}".format(mean)
    std = result.stats["std"]
    assert 0.3 < std < 0.5, "std: {}".format(std)
    sse = result.stats["sse"]
    assert 2.05 < sse < 2.4, "sse: {}".format(sse)


def main():
    all_trajectory_files = load_all_txt_files("/testroot")
    reference_trajectory_file = "/testroot/data/MH_01_easy.txt"
    for trajectory_file in all_trajectory_files:
        print("Evaluating", trajectory_file)
        evaluate_trajectory(reference_trajectory_file, trajectory_file)


if __name__ == "__main__":
    main()
