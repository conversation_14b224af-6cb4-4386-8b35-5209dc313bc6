#!/bin/bash

set -eoux pipefail

/buildroot/build-Release/euroc_main \
  --calib_file "/testroot/code/test/test_data/euroc/euroc.yaml" \
  --config_file "/testroot/code/test/test_data/euroc/setting.yaml" \
  --data_directory "/testroot/data/mav0" \
  --log_file "/testroot/results_MH_01_easy.txt" \
  --odometry_log_file "/testroot/results_MH_01_easy_odometry.csv" \
  --start_offset_sec 25 \
  --duration_sec 30
