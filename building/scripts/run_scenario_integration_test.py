import logging
import os
import subprocess
import time

import evo.main_rpe as main_rpe
import mlflow
import numpy as np
from evo.core import sync
from evo.core.metrics import PoseRelation
from evo.core.units import Unit
from evo.tools import file_interface
from mlflow import MlflowClient

os.environ["MLFLOW_TRACKING_URI"] = (
    "http://gitlab.int.spleenlab.ai:20080/api/v4/projects/792/ml/mlflow/"
)

TEST_DATA_DIR = "http://nexus.int.spleenlab.ai:30001/repository/test-data/spl_vio/test_scenarios"

SCENARIOS = {
    "MH_01_EASY": {
        "DATA_PATH_NEXUS": os.path.join(TEST_DATA_DIR, "MH_01_easy", "zip", "MH_01_easy.zip"),
        "GT_PATH_NEXUS": os.path.join(TEST_DATA_DIR, "MH_01_easy", "eval", "gt_trajectory.txt"),
        "SETTINGS_PATH": "/testroot/code/test/test_data/euroc/setting.yaml",
        "SENSOR_CALIB_PATH": "/testroot/code/test/test_data/euroc/euroc.yaml",
        "MAX_RPE_RMSE": 1.011213,
        "MAX_RPE_SSE": 82.51,
        # WARN (VIPUL): The previous value was 83.09 because the Euroc dataset loader was using opencv's undistort
        # and now, we are using
        # spl-camera-models undistortion. With this new undistortion method, the todo below can't be achieved anymore
        # because the value
        # 81.803917 was obtained using opencv's undistortion.
        # TODO (Vipul): Replace this value with 81.803917 as soon as spl-camera-models supports double.
    },
    "MH_03_MEDIUM": {
        "DATA_PATH_NEXUS": os.path.join(TEST_DATA_DIR, "MH_03_medium", "zip", "MH_03_medium.zip"),
        "GT_PATH_NEXUS": os.path.join(TEST_DATA_DIR, "MH_03_medium", "eval", "gt_trajectory.txt"),
        "SETTINGS_PATH": "/testroot/code/test/test_data/euroc/setting.yaml",
        "SENSOR_CALIB_PATH": "/testroot/code/test/test_data/euroc/euroc.yaml",
        "MAX_RPE_RMSE": 1.401503,
        "MAX_RPE_SSE": 217.52
        # WARN (VIPUL): The previous value was 218.174023  because the Euroc dataset loader was using opencv's
        # undistort and now, we are using
        # spl-camera-models undistortion. With this new undistortion method, the todo below can't be achieved anymore
        # because the value
        # 219.991491 was obtained using opencv's undistortion.
        # TODO (Vipul): Replace this value with 219.991491 as soon as spl-camera-models supports double.
    },
    "SIMULATION_01": {
        "DATA_PATH_NEXUS": os.path.join(TEST_DATA_DIR, "Simulation_01", "zip", "Simulation_01.zip"),
        "GT_PATH_NEXUS": os.path.join(TEST_DATA_DIR, "Simulation_01", "eval", "gt_trajectory.txt"),
        "SETTINGS_PATH": "/testroot/code/test/test_data/euroc/sim_setting.yaml",
        "SENSOR_CALIB_PATH": "/testroot/code/test/test_data/euroc/sim_sensor.yaml",
        "MAX_RPE_RMSE": 0.235,
        "MAX_RPE_SSE": 12.768,
    }
}

logging.basicConfig(
    level=logging.INFO,  # Set the root logging level to DEBUG
    format="%(asctime)s-%(module)-20s %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
)


def download_file_from_nexus(url, target):
    nexus_user = os.environ["NEXUS_USER"]
    nexus_pass = os.environ["NEXUS_PASSWORD"]
    try:
        subprocess.run(
            [
                "curl",
                "--fail",
                "-u",
                f"{nexus_user}:{nexus_pass}",
                "-o",
                f"{target}",
                f"{url}",
            ],
            check=True,
            capture_output=True,
            text=True,
        )
        logging.info("Download successful.")

    except subprocess.CalledProcessError as e:
        logging.info(f"Download failed with return code {e.returncode}")
        logging.info("stdout:", e.stdout)
        logging.info("stderr:", e.stderr)
        exit(1)


def get_local_or_from_nexus(filepath, scenario_dir):
    file_name = os.path.basename(filepath)
    if "http" in filepath:
        # Assuming its on Nexus
        path = os.path.join(scenario_dir, file_name)
        download_file_from_nexus(url=filepath, target=path)
        filepath = path
    return filepath


def log_metrics_to_mlflow(dataset_name, branch_name, metrics):
    client = MlflowClient()
    experiment_name = dataset_name
    # Check if experiment is already created (E.g. the scenario exists)
    try:
        experiment_id = client.create_experiment(
            name=experiment_name,
        )
    except Exception as e:
        # If the experiment already exists or another error occurred, try to get the experiment by name
        logging.info(
            f"Could not create experiment '{experiment_name}'. Attempting to get existing one."
        )
        logging.info(e)
        experiment = client.get_experiment_by_name(experiment_name)
        if experiment is None:
            raise ValueError(
                f"ERROR: Could not create or find the experiment named '{experiment_name}'."
            )
        experiment_id = experiment.experiment_id

    # Now log the collected data
    try:
        logging.info(f"Creating run for {experiment_id}")
        tags = {"branch": branch_name, "commit_sha": os.environ["CI_COMMIT_SHA"]}
        run = client.create_run(
            experiment_id=experiment_id, tags=tags, run_name=branch_name
        )

        run_id = run.info.run_id
        mlflow.start_run(run_id=run_id)
        for k, v in metrics.items():
            client.log_metric(run_id, k, v)

        # Additionally we can associate the RUN with the Gitlab branch
        if os.getenv('CI_JOB_ID'):
            print("Setting Job ID.")
            mlflow.set_tag('gitlab.CI_JOB_ID', os.getenv('CI_JOB_ID'))

        mlflow.end_run()
        logging.info(f"Experiment ID: {experiment_id}, Run ID: {run_id}")
        logging.info("Metrics successfully logged to MLflow.")

    except Exception as e:
        logging.info(e.with_traceback())


def main():
    logging.info("Scenario Runner Started.")

    if "VERSION" in os.environ:
        branch_name = os.environ["VERSION"]
    else:
        branch_name = os.environ["CI_COMMIT_BRANCH"]

    # Run Scenario(s)
    for scenario_name, scenario_meta in SCENARIOS.items():
        logging.info(f"Running scenario: {scenario_name}")

        # Scneario Workingdirectory
        scenario_dir = os.path.join("tmp", scenario_name, "data")
        os.makedirs(scenario_dir, exist_ok=True)
        logging.info(f"Created directory: {scenario_dir}")

        # Scenario ASL Data
        zip_data_filename = os.path.join(scenario_dir, "data_file.zip")
        logging.info(f"Downloading {zip_data_filename} into {scenario_dir}")
        download_file_from_nexus(
            url=scenario_meta["DATA_PATH_NEXUS"], target=zip_data_filename
        )
        subprocess.run(
            ["unzip", zip_data_filename, "-d", scenario_dir],
            check=True,
            capture_output=True,
            text=True,
        )

        # Sensor Calib and Setting
        setting_yaml = get_local_or_from_nexus(
            scenario_meta["SETTINGS_PATH"], scenario_dir
        )
        sensor_yaml = get_local_or_from_nexus(
            scenario_meta["SENSOR_CALIB_PATH"], scenario_dir
        )

        # Run Scenario
        trajectory_prediction_filename = os.path.join(scenario_dir, "trajectory_prediction.txt")
        odometry_prediction_filename = os.path.join(scenario_dir, "odometry_prediction.csv")
        application_command = [
            "/buildroot/build-Release/euroc_main",
            "--calib_file",
            sensor_yaml,
            "--config_file",
            setting_yaml,
            "--data_directory",
            os.path.join(scenario_dir, "mav0"),
            "--log_file",
            trajectory_prediction_filename,
            "--odometry_log_file",
            odometry_prediction_filename,
            "--start_offset_sec",
            "0",
            "--duration_sec",
            "3000000",
        ]

        logging.info(f"Running Recompute: {application_command}")
        start_time = time.time()
        subprocess.run(application_command, check=True, capture_output=True, text=True)
        end_time = time.time()
        execution_time = end_time - start_time
        logging.info(f"Recomput {scenario_name} in {execution_time:.4f}s completed.")

        logging.info("Getting GT.")
        gt_url = scenario_meta["GT_PATH_NEXUS"]
        gt_filename = os.path.join(scenario_dir, "ground_truth.txt")
        download_file_from_nexus(url=gt_url, target=gt_filename)

        logging.info(f"Running Evaluation {trajectory_prediction_filename} {gt_filename}")
        traj_ref = file_interface.read_tum_trajectory_file(gt_filename)
        traj_est = file_interface.read_tum_trajectory_file(trajectory_prediction_filename)
        traj_ref, traj_est = sync.associate_trajectories(traj_ref, traj_est)
        result = main_rpe.rpe(
            traj_ref,
            traj_est,
            pose_relation=PoseRelation.full_transformation,
            delta=1.0,
            delta_unit=Unit.meters,
        )
        logging.info(result)
        log_metrics_to_mlflow(
            dataset_name=scenario_name, branch_name=branch_name, metrics=result.stats
        )
        logging.info("Checking metrics")

        rmse = result.stats["rmse"]
        assert np.allclose(rmse, scenario_meta["MAX_RPE_RMSE"], atol=0.01), "rmse: {}".format(rmse)
        sse = result.stats["sse"]
        assert np.allclose(sse, scenario_meta["MAX_RPE_SSE"], atol=0.01), "sse: {}".format(sse)

        logging.info(f"Completed {scenario_name}")

    logging.info("All Scenarios recomputed!")


if __name__ == "__main__":
    main()
