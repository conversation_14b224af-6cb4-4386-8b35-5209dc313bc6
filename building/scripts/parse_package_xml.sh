#!/bin/bash

set -eoux pipefail

# NOTE(Jack): Executed within Docker image during build process
PACKAGE_XML_PATH="/temporary/package.xml"


if [ -z "$SPL_LIBRARY_NAME" ]
then
      SPL_LIBRARY_NAME=$(xmlstarlet sel -t -v "package/name" ${PACKAGE_XML_PATH})
      export SPL_LIBRARY_NAME
else
      echo "SPL_LIBRARY_NAME already set to $SPL_LIBRARY_NAME! Not parsed from package.xml!"
fi

SPL_LIBRARY_VERSION=$(xmlstarlet sel -t -v "package/version" ${PACKAGE_XML_PATH})
export SPL_LIBRARY_VERSION
SPL_LIBRARY_REVISION=$(xmlstarlet sel -t -v "package/revision" ${PACKAGE_XML_PATH})
export SPL_LIBRARY_REVISION
SPL_LIBRARY_MAINTAINER=$(xmlstarlet sel -t -v "package/name" ${PACKAGE_XML_PATH})
export SPL_LIBRARY_MAINTAINER
SPL_LIBRARY_EMAIL=$(cat ${PACKAGE_XML_PATH} | grep -i "<maintainer email=" | cut --delimiter="\"" -f2)
export SPL_LIBRARY_EMAIL
SPL_LIBRARY_DESCRIPTION=$(xmlstarlet sel -t -v "package/description" ${PACKAGE_XML_PATH})
export SPL_LIBRARY_DESCRIPTION
SPL_LIBRARY_LICENSE=$(xmlstarlet sel -t -v "package/license" ${PACKAGE_XML_PATH})
export SPL_LIBRARY_LICENSE