#!/bin/bash

set -eoux pipefail

GITLAB_TOKEN=$(</root/gitlabtoken)

git clone http://"${GITLAB_TOKEN}"@gitlab.int.spleenlab.ai:20080/calibration-and-perception/spl-camera-models.git

SPL_CAMERA_MODELS_VERSION=5.1.0
SPL_CAMERA_MODELS_REVISION="release"

cd /buildroot/spl-camera-models
git checkout "${SPL_CAMERA_MODELS_VERSION}"

# We have to move the directory with all the code into /buildroot, because the build scripts have hardcoded paths
mv /buildroot/spl-camera-models/spl_camera_models_cpp /buildroot

# We load the package.xml and then overwrite the default version and revision so that the library/debian package
# will have the right name and version numbers
# shellcheck disable=SC1091
SPL_LIBRARY_NAME="spl-camera-models" source /buildroot/spl-camera-models/building/script/parse_package_xml.sh
export SPL_LIBRARY_VERSION="${SPL_CAMERA_MODELS_VERSION}"
export SPL_LIBRARY_REVISION="${SPL_CAMERA_MODELS_REVISION}"

BUILD_TYPE=RelWithDebInfo
BUILD_APPLICATIONS=OFF
BUILD_TESTING=ON
PACKAGE_LIBRARY=ON
SPL_LIBRARY_NAME="spl-camera-models" /buildroot/spl-camera-models/building/script/build_deb.sh "${BUILD_TYPE}" "${BUILD_APPLICATIONS}" "${BUILD_TESTING}" "${PACKAGE_LIBRARY}"

rm --recursive --force /buildroot/build-RelWithDebInfo

# We need the wildcard `_*.deb` because this needs to automatically work for both the arm and amd systems
dpkg --install /buildroot/debian-files/spl-camera-models-dev_"${SPL_CAMERA_MODELS_VERSION}"-"${SPL_CAMERA_MODELS_REVISION}"_*.deb
