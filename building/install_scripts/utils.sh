#!/bin/bash

function download_script() {
  local url="$1"
  local url_format_suffix="?ref_type=heads"
  local filename="${url##*/}"
  local file_path="/buildroot/${filename}"

  curl --fail "${url}${url_format_suffix}" --output "${file_path}"

  chmod +x "${file_path}"
}

function configure_nexus() {
  download_script "http://gitlab.int.spleenlab.ai:20080/spleenlab-public/public-install-dependencies/-/raw/master/apt/ubuntu-generic/1.4.0/configure-nexus.sh"

  /buildroot/configure-nexus.sh "${SPL_REPOSITORY}" "${SPL_NETWORK}"
  rm /buildroot/configure-nexus.sh
}

function unconfigure_nexus() {
  download_script "http://gitlab.int.spleenlab.ai:20080/spleenlab-public/public-install-dependencies/-/raw/master/apt/ubuntu-generic/1.4.0/undo-configure-nexus.sh"

  /buildroot/undo-configure-nexus.sh
  rm /buildroot/undo-configure-nexus.sh
}

function install_spl_library() {
  local package="${1}"
  local version="${2}"
  local revision="${3}"
  curl http://gitlab.int.spleenlab.ai:20080/spleenlab-public/public-install-dependencies/-/raw/master/spl-libraries/ubuntu-generic/1.0.3/install_spl_library.py?format=TEXT | \
  python3 - --library-name "${package}"  --version "${version}" --revision "${revision}" "${@:4}"
}
