#!/bin/bash

set -eoux pipefail

# shellcheck disable=SC1091
source /temporary/building/install_scripts/utils.sh

configure_nexus

apt-get update
apt-get satisfy --yes --no-install-recommends \
    "build-essential (>= 12.9),
     cmake (>= 3.22),
     file (>= 1:5),
     gpg (>= 2.2),
     ninja-build (>= 1.10),
     xmlstarlet (>= 1.6)"

rm --force --recursive /var/lib/apt/lists/*

unconfigure_nexus

