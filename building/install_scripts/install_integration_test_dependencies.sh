#!/bin/bash

set -eoux pipefail

# shellcheck disable=SC1091
source /temporary/building/install_scripts/utils.sh

configure_nexus

apt-get update
apt-get install --yes python3.10-venv

python3 -m venv env
# shellcheck disable=SC1091
source /buildroot/env/bin/activate

pip3 install --upgrade --force-reinstall python-dateutil
pip3 install evo mlflow

rm --force --recursive /var/lib/apt/lists/*

unconfigure_nexus

