# Modified version of: https://github.com/googleapis/google-cloud-cpp/blob/main/.clang-format
# Use the Google style in this project.
BasedOnStyle: Google

# Some folks prefer to write "int& foo" while others prefer "int &foo".  The
# Google Style Guide only asks for consistency within a project, we chose
# "int& foo" for this project:
DerivePointerAlignment: false
PointerAlignment: Left

# The Google Style Guide only asks for consistency w.r.t. "east const" vs.
# "const west" alignment of cv-qualifiers. At Spleenlab we use "const west".
QualifierAlignment: Left

# Include main header and gtest header first, system includes second and project includes last.
IncludeIsMainRegex: '()$?'
IncludeCategories:
  - Regex: '^((<|")(gtest)/)'
    Priority: -100
  - Regex: '^<[^/.]*\.(h)>'
    Priority: 50
  - Regex: '^<[^/.]*>'
    Priority: 100
  - Regex: '^<experimental/[^/.]*>'
    Priority: 100
  - Regex: '^<.*>'
    Priority: 200
  - Regex: '".*"'
    Priority: 300

# Use Spleenlabs' softer 120 chars limit instead of 80 chars.
ColumnLimit: 120

# Make sure to prevent single line if statements without braces.
InsertBraces: true
